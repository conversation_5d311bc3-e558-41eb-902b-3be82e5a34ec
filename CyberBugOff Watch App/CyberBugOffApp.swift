//
//  CyberBugOffApp.swift
//  CyberBugOff Watch App
//
//  Created by 吕海峰 on 2025/6/12.
//

import SwiftUI

@main
struct CyberBugOffApp: App {
    // 全局模型实例
    @StateObject private var model = BugOffModel()
    
    init() {
        // 启动时清除所有用户缓存，方便测试
        clearAllUserCaches()
    }

    // 清除所有用户缓存和数据
    private func clearAllUserCaches() {
        print("🧹 开始清除所有用户缓存...")

        // 1. 清除 UserDefaults 中的所有应用数据
        clearUserDefaults()

        // 2. 清除内存缓存
        clearMemoryCaches()

        // 3. 清除磁盘缓存
        clearDiskCaches()

        // 4. 清除用户添加的图片文件
        clearUserAddedImageFiles()

        print("✅ 所有用户缓存清除完成")
    }

    // 清除 UserDefaults 数据
    private func clearUserDefaults() {
        let userDefaults = UserDefaults.standard
        let domain = Bundle.main.bundleIdentifier!
        userDefaults.removePersistentDomain(forName: domain)
        userDefaults.synchronize()
        print("🗑️ UserDefaults 数据已清除")
    }

    // 清除内存缓存
    private func clearMemoryCaches() {
        // 清除图片显示缓存 (ImageManager)
        ImageManager.clearDisplayImageCache()

        // 清除缩略图缓存
        ThumbnailGenerator.invalidateAll()

        // 清除 Toast 图片缓存
        TriggerManager.clearToastImageCache()

        print("🧠 内存缓存已清除")
    }

    // 清除磁盘缓存
    private func clearDiskCaches() {
        // 清除缩略图磁盘缓存
        ThumbnailGenerator.clearDiskCache()

        print("💾 磁盘缓存已清除")
    }

    // 清除用户添加的文件（图片和音频）
    private func clearUserAddedImageFiles() {
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!

        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: documentsDirectory, includingPropertiesForKeys: nil)
            for fileURL in fileURLs {
                let ext = fileURL.pathExtension.lowercased()
                // 删除用户添加的图片文件
                if ext == "jpg" || ext == "png" {
                    try fileManager.removeItem(at: fileURL)
                    print("🗑️ 删除用户图片文件: \(fileURL.lastPathComponent)")
                }
                // 删除用户添加的音频文件（自定义音效和合成音效）
                else if ext == "mp3" || ext == "m4a" {
                    try fileManager.removeItem(at: fileURL)
                    print("🗑️ 删除用户音频文件: \(fileURL.lastPathComponent)")
                }
            }
        } catch {
            print("⚠️ 清除用户文件时出错: \(error)")
        }
    }
    
    var body: some Scene {
        WindowGroup {
            ImageModeView(model: model)
                // 注入主模型及其子 manager，供后续视图细化订阅
                .environmentObject(model)
                .environmentObject(model.imageManager)
                .environmentObject(model.soundManager)
                .environmentObject(model.triggerManager)
                .onAppear {
                    // 在视图出现后清除音频缓存（此时 model 已经创建）
                    clearAudioCaches()
                }
        }
    }

    // 清除音频相关缓存
    private func clearAudioCaches() {
        // 清除音频数据缓存
        model.soundManager.audioService.clearSoundDataCache()
        print("🎵 音频缓存已清除")
    }
}
