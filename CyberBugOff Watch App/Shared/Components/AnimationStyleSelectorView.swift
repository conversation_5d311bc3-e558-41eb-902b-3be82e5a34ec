import SwiftUI

/// 动画样式选择器组件 - 可复用的动画样式设置UI
struct AnimationStyleSelectorView: View {
    @Binding var selectedStyle: TriggerAnimationStyle
    @Binding var isExpanded: Bool
    let onStyleChanged: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 动画样式标题行
            Button(action: {
                withAnimation(.spring(response: 0.35, dampingFraction: 0.8)) {
                    isExpanded.toggle()
                }
            }) {
                StandardRowContent(
                    leftIcon: "sparkles",
                    leftTitle: "动画样式",
                    rightText: selectedStyle.rawValue,
                    isExpanded: isExpanded
                )
                .standardRowStyle()
                .contentShape(Rectangle())
            }
            .buttonStyle(PlainButtonStyle())
            
            // 动画样式选择器 - 只在展开时显示
            if isExpanded {
                animationStylePickerView
                    .padding(.top, AppTheme.smallPadding)
                    .transition(.asymmetric(
                        insertion: .opacity.combined(with: .scale(scale: 0.8, anchor: .top)),
                        removal: .opacity.combined(with: .scale(scale: 0.8, anchor: .top))
                    ))
            }
        }
        .animation(.spring(response: 0.35, dampingFraction: 0.8), value: isExpanded)
    }
    
    /// 动画样式选择器视图
    private var animationStylePickerView: some View {
        ScrollView(.vertical, showsIndicators: false) {
            VStack(spacing: AppTheme.smallPadding) {
                ForEach(TriggerAnimationStyle.allCases, id: \.self) { style in
                    Button(action: {
                        // 更新配置
                        selectedStyle = style
                        onStyleChanged()
                        
                        // 选择后自动关闭列表
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.9)) {
                            isExpanded = false
                        }
                    }) {
                        HStack {
                            Image(systemName: AnimationStyleHelper.getIcon(for: style))
                                .foregroundColor(AppTheme.primaryColor)
                                .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                            Text(style.rawValue)
                                .font(.appBody)
                                .foregroundColor(Color.textPrimary)
                            Spacer()
                            if selectedStyle == style {
                                Image(systemName: "checkmark")
                                    .foregroundColor(AppTheme.primaryColor)
                            }
                        }
                        .frame(height: AppTheme.rowHeight)
                        .padding(.horizontal)
                        .background(AppTheme.secondaryBackgroundColor.opacity(0.3))
                        .cornerRadius(AppTheme.cornerRadius)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .frame(height: AppTheme.pickerHeight)
        .padding(.horizontal, AppTheme.mediumPadding)
    }
}

/// 动画样式辅助工具类
struct AnimationStyleHelper {
    /// 为动画样式选择合适的图标
    static func getIcon(for style: TriggerAnimationStyle) -> String {
        switch style {
        case .scale:
            return "arrow.up.and.down"
        case .bounce:
            return "arrow.up.arrow.down"
        case .rotate:
            return "arrow.clockwise"
        case .fade:
            return "wand.and.stars"
        case .slide:
            return "arrow.right"
        case .heart:
            return "heart.fill"
        // 新增高级动画图标
        case .flip:
            return "flip.horizontal"
        case .wave:
            return "waveform"
        case .pulse:
            return "dot.radiowaves.left.and.right"
        case .sparkle:
            return "sparkles"
        case .spiral:
            return "tornado"
        case .shake:
            return "chevron.left.forwardslash.chevron.right"
        }
    }
}

#Preview {
    @Previewable @State var selectedStyle: TriggerAnimationStyle = .bounce
    @Previewable @State var isExpanded: Bool = false
    
    return AnimationStyleSelectorView(
        selectedStyle: $selectedStyle,
        isExpanded: $isExpanded,
        onStyleChanged: {
            print("Style changed to: \(selectedStyle)")
        }
    )
}
