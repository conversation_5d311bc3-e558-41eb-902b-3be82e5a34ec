import SwiftUI
import Foundation

// MARK: - UnifiedSwipableRow
/// 统一的可滑动行组件，同时支持音效合成视图和音效列表视图的需求
struct UnifiedSwipableRow: View {
    // 必填基本参数
    let sound: String
    @ObservedObject var model: BugOffModel
    @EnvironmentObject private var soundManager: SoundManager
    let imageName: String?
    @Binding var playingSounds: Set<String>
    
    // 编辑相关参数
    @Binding var soundToEdit: String?
    @Binding var isShowingEditSheet: Bool
    let onEnterEditMode: (() -> Void)?
    
    // 列表模式相关参数
    let mode: SoundListMode?
    @Binding var selectedItems: [String]
    @Binding var selectedSounds: Set<String>
    let updateImageSounds: (() -> Void)?
    var onSoundsUpdated: (() -> Void)?
    
    // 动作处理
    let duplicateSound: (() -> Void)?
    let deleteSound: ((String) -> Void)
    let onStartIndividualPlayback: (() -> Void)?
    
    // 侧滑提示
    let shouldShowHint: Bool
    let swipeHintManager: AppTheme.SwipeHintManager?
    let hintStyle: AppTheme.SwipeHintStyle

    // 音效合成相关
    let mixerSelectedSounds: [String]?

    // 内部状态
    @State private var animationOffset: CGFloat = 0
    
    // MARK: - 初始化器
    /// 音效合成视图中使用的初始化器
    init(sound: String,
         model: BugOffModel,
         selectedSounds: [String],
         playingSounds: Binding<Set<String>>,
         soundToEdit: Binding<String?>,
         isShowingEditSheet: Binding<Bool>,
         duplicateSound: @escaping () -> Void,
         deleteSound: @escaping () -> Void,
         imageName: String?,
         onEnterEditMode: @escaping () -> Void,
         onStartIndividualPlayback: @escaping () -> Void,
         shouldShowHint: Bool,
         swipeHintManager: AppTheme.SwipeHintManager?,
         hintStyle: AppTheme.SwipeHintStyle) {
        
        self.sound = sound
        self.model = model
        self.imageName = imageName
        self._playingSounds = playingSounds
        self._soundToEdit = soundToEdit
        self._isShowingEditSheet = isShowingEditSheet
        self.duplicateSound = duplicateSound
        self.deleteSound = { _ in deleteSound() }
        self.onEnterEditMode = onEnterEditMode
        self.onStartIndividualPlayback = onStartIndividualPlayback
        self.shouldShowHint = shouldShowHint
        self.swipeHintManager = swipeHintManager
        self.hintStyle = hintStyle
        self.mixerSelectedSounds = selectedSounds

        // 列表模式相关参数(不使用，但需要初始化)
        self.mode = nil
        self._selectedItems = .constant([])
        self._selectedSounds = .constant(Set<String>())
        self.updateImageSounds = nil
        self.onSoundsUpdated = nil
    }
    
    /// 音效列表中使用的初始化器
    init(sound: String,
         model: BugOffModel,
         playingSounds: Binding<Set<String>>,
         selectedItems: Binding<[String]>,
         selectedSounds: Binding<Set<String>>,
         soundToEdit: Binding<String?>,
         isShowingEditSheet: Binding<Bool>,
         updateImageSounds: @escaping () -> Void,
         onSoundsUpdated: (() -> Void)?,
         deleteSound: @escaping (String) -> Void,
         imageName: String?,
         shouldShowHint: Bool,
         swipeHintManager: AppTheme.SwipeHintManager?,
         hintStyle: AppTheme.SwipeHintStyle,
         mode: SoundListMode) {
        
        self.sound = sound
        self.model = model
        self.imageName = imageName
        self._playingSounds = playingSounds
        self._soundToEdit = soundToEdit
        self._isShowingEditSheet = isShowingEditSheet
        self.deleteSound = deleteSound
        self.shouldShowHint = shouldShowHint
        self.swipeHintManager = swipeHintManager
        self.hintStyle = hintStyle
        
        // 音效列表特有参数
        self.mode = mode
        self._selectedItems = selectedItems
        self._selectedSounds = selectedSounds
        self.updateImageSounds = updateImageSounds
        self.onSoundsUpdated = onSoundsUpdated
        
        // 合成视图特有参数(不使用，但需要初始化)
        self.duplicateSound = nil
        self.onEnterEditMode = nil
        self.onStartIndividualPlayback = nil
        self.mixerSelectedSounds = nil
    }
    
    var body: some View {
        HStack {
            // 选择框区域 - 仅在列表模式显示
            if mode != nil {
                UnifiedSelectionView(
                    sound: sound,
                    items: selectedItems,
                    isSequential: soundManager.soundPlayMode == .sequential
                )
                .onTapGesture {
                    handleSelectionTap()
                }
            }
            
            // 名称显示
            Text(getDisplayName()) // 通过SoundDisplayNameManager获取最新显示名称
                .font(.appBody)
                .foregroundColor(.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)
                .contentShape(Rectangle())
                .onTapGesture {
                    // 总是先停止提示动画
                    stopHintAnimationIfNeeded()

                    // 然后处理选择逻辑（仅在列表模式）
                    if mode != nil {
                        handleSelectionTap()
                    }
                }
            
            // 播放按钮
            Button(action: togglePlayback) {
                Image(systemName: playingSounds.contains(sound) ? "pause.fill" : "play.fill")
            }
            .buttonStyle(PlayPauseButtonStyle(isPlaying: playingSounds.contains(sound)))
        }
        .frame(height: AppTheme.rowHeight)
        .padding(.vertical, AppTheme.smallPadding)
        .offset(x: animationOffset)
        .onAppear {
            // 恢复提示动画，但使用轻量级实现
            if shouldShowHint, let manager = swipeHintManager {
                let offsetBinding = Binding<CGFloat>(
                    get: { animationOffset },
                    set: { animationOffset = $0 }
                )

                // 延迟执行动画，避免与视图创建冲突
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    manager.performHint(
                        for: sound,
                        offset: offsetBinding,
                        style: intelligentHintStyle
                    )
                }
            }
        }
        .onTapGesture {
            // 用户点击时立即停止提示动画
            stopHintAnimationIfNeeded()
        }
        .gesture(
            // 检测拖拽手势，立即停止提示动画
            DragGesture(minimumDistance: 5)
                .onChanged { _ in
                    stopHintAnimationIfNeeded()
                }
        )
        .swipeActions(edge: .leading) {
            // 显示编辑按钮：合成视图(nil)、首页音效列表(.edit)
            // mode设置模式下的编辑按钮移到右滑
            if mode == nil || mode == .edit {
                editButton
            }
        }
        .swipeActions(edge: .trailing) {
            // mode设置模式：右滑显示设置按钮（替换删除按钮位置）
            if mode == .modeSettings {
                editButton
            } else {
                // 其他模式：右滑显示删除按钮
                // 音效合成模式下，如果只剩一个音效则不显示删除按钮
                if let mixerSounds = mixerSelectedSounds, mixerSounds.count <= 1 {
                    // 音效合成模式且只剩一个音效：不显示删除按钮
                    EmptyView()
                } else {
                    // 显示删除按钮
                    Button(role: .destructive) {
                        // 停止提示动画
                        stopHintAnimationIfNeeded()
                        deleteSound(sound)
                    } label: {
                        VStack(spacing: 2) {
                            Image(systemName: "trash")
                                .font(.system(size: AppTheme.smallIconSize, weight: .medium))
                        }
                        .foregroundColor(.white)
                    }
                }
            }
            // 只在合成视图且非同时播放模式下显示复制按钮
            if mode == nil && duplicateSound != nil {
                duplicateButton
            }
        }
    }
    
    // MARK: - 子视图
    private var editButton: some View {
        Button {
            // 停止提示动画
            stopHintAnimationIfNeeded()

            // 停止所有音效
            model.stopSound()
            playingSounds.removeAll()

            // 通知重置播放状态
            onEnterEditMode?()

            // 设置编辑目标
            soundToEdit = sound
            model.selectedSound = sound

            // 尝试设置isShowingEditSheet为true
            // 如果传入的是.constant(false)，这个设置不会生效，从而支持navigationDestination模式
            isShowingEditSheet = true
        } label: {
            VStack(spacing: 2) {
                let isModeSettings = (mode == .some(.modeSettings))
                let isMixerMode = (mode == nil) // 音效合成模式
                let iconName = (isModeSettings || isMixerMode) ? "gearshape" : "pencil"
                Image(systemName: iconName)
                    .font(.system(size: AppTheme.smallIconSize, weight: .medium))
            }
            .foregroundColor(.white)
        }
        .tint(AppTheme.primaryColor)
    }
    
    private var duplicateButton: some View {
        Button(action: {
            // 停止提示动画
            stopHintAnimationIfNeeded()
            duplicateSound?()
        }) {
            VStack(spacing: 2) {
                Image(systemName: "plus.square.on.square")
                    .font(.system(size: AppTheme.smallIconSize, weight: .medium))
            }
            .foregroundColor(.white)
        }
        .tint(AppTheme.successColor)
    }
    
    // MARK: - 辅助方法

    /// 获取音效的显示名称
    private func getDisplayName() -> String {
        // 现在sound参数已经是最新的显示名称，直接返回即可
        // 这是因为SoundListView现在使用currentSoundDisplayNames来提供最新的显示名称
        return sound
    }

    // MARK: - 动画控制

    /// 根据实际的侧滑按钮配置智能确定动画样式
    private var intelligentHintStyle: AppTheme.SwipeHintStyle {
        let hasLeadingActions = hasLeadingSwipeActions
        let hasTrailingActions = hasTrailingSwipeActions

        if hasLeadingActions && hasTrailingActions {
            // 两边都有按钮：使用双向动画，从右开始（因为右滑更常用）
            return .bidirectional(.right)
        } else if hasLeadingActions {
            // 只有左滑按钮：使用单向右滑动画（提示左滑操作）
            return .single(.right)
        } else if hasTrailingActions {
            // 只有右滑按钮：使用单向左滑动画（提示右滑操作）
            return .single(.left)
        } else {
            // 没有侧滑按钮：使用摆动动画作为通用提示
            return .wiggle
        }
    }

    /// 检查是否有左滑按钮
    private var hasLeadingSwipeActions: Bool {
        // 显示编辑按钮：合成视图(nil)、首页音效列表(.edit)
        return mode == nil || mode == .edit
    }

    /// 检查是否有右滑按钮
    private var hasTrailingSwipeActions: Bool {
        // mode设置模式：右滑显示设置按钮
        if mode == .modeSettings {
            return true
        }

        // 其他模式：检查是否有删除按钮或复制按钮
        let hasDeleteButton: Bool
        if let mixerSounds = mixerSelectedSounds, mixerSounds.count <= 1 {
            // 音效合成模式且只剩一个音效：没有删除按钮
            hasDeleteButton = false
        } else {
            // 有删除按钮
            hasDeleteButton = true
        }

        // 检查是否有复制按钮（只在合成视图且非同时播放模式下显示）
        let hasDuplicateButton = mode == nil && duplicateSound != nil

        return hasDeleteButton || hasDuplicateButton
    }

    private func stopHintAnimationIfNeeded() {
        if let manager = swipeHintManager, manager.isCurrentlyAnimating {
            let offsetBinding = Binding<CGFloat>(
                get: { animationOffset },
                set: { animationOffset = $0 }
            )
            manager.stopCurrentAnimation(offset: offsetBinding)
        }
    }

    // MARK: - 动作
    private func togglePlayback() {
        // 停止提示动画
        stopHintAnimationIfNeeded()

        if playingSounds.contains(sound) {
            model.stopSound()
            playingSounds.remove(sound)
        } else {
            model.stopSound()
            playingSounds.removeAll()
            
            // 通知重置播放状态
            onStartIndividualPlayback?()
            
            // 播放音效
            if let imageName = imageName {
                // Mode相关预览：使用mode特定配置
                model.playSound(soundName: sound, for: imageName) {
                    playingSounds.remove(sound)
                }
            } else if mode == nil {
                // 音效合成模式：使用音效合成的临时配置
                let config = model.soundManager.getSoundConfigForMixer(for: sound)
                model.soundManager.playSound(soundName: sound, config: config) {
                    playingSounds.remove(sound)
                }
            } else {
                // 首页soundlist视图等其他模式：使用全局默认配置
                model.playSound(soundName: sound) {
                    playingSounds.remove(sound)
                }
            }
            playingSounds.insert(sound)
        }
    }
    
    // 处理选择点击（仅在列表模式生效）
    private func handleSelectionTap() {
        // 停止提示动画
        stopHintAnimationIfNeeded()

        guard let updateImageSounds = updateImageSounds else { return }
        
        withAnimation(.standardAnimation()) {
            if let index = selectedItems.firstIndex(of: sound) {
                selectedItems.remove(at: index)
                if let orderIndex = soundManager.selectedSoundsOrder.firstIndex(of: sound) {
                    soundManager.selectedSoundsOrder.remove(at: orderIndex)
                }
            } else {
                selectedItems.append(sound)
                if !soundManager.selectedSoundsOrder.contains(sound) {
                    soundManager.selectedSoundsOrder.append(sound)
                }
            }
            selectedSounds = Set(selectedItems)
            updateImageSounds()
            onSoundsUpdated?()
        }
    }
}
