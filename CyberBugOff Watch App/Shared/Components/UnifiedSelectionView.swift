import SwiftUI

/// 统一的音效选择指示器视图，显示选中状态和序号
struct UnifiedSelectionView: View {
    let sound: String
    let items: [String]
    let isSequential: Bool
    
    var body: some View {
        ZStack {
            // 背景圆圈 - 无论选中与否都保持相同尺寸
            if isSelected {
                Circle()
                    .fill(AppTheme.primaryColor)
                    .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
            } else {
                Circle()
                    .stroke(Color.disabled, lineWidth: 1)
                    .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
            }
            
            // 填充圆圈 - 选中时显示
            // 内容 - 选中时显示序号（根据点击顺序）
            if isSelected {
                Text("\(getSequenceNumber())")
                    .font(.appSmall)
                    .foregroundColor(.white)
                    .transition(.opacity)
            }
        }
        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
        // 使用组合动画，确保平滑过渡
        .animation(.standardAnimation(), value: isSequential)
        .animation(.standardAnimation(), value: isSelected)
    }
    
    private var isSelected: Bool {
        return items.contains(sound)
    }
    
    private func getSequenceNumber() -> Int {
        if let index = items.firstIndex(of: sound) {
            return index + 1
        }
        return 0
    }
}

#Preview {
    HStack(spacing: 20) {
        // 未选择状态
        UnifiedSelectionView(
            sound: "sound1", 
            items: ["sound2", "sound3"], 
            isSequential: false
        )
        
        // 选择状态 - 打勾模式
        UnifiedSelectionView(
            sound: "sound2", 
            items: ["sound2", "sound3"], 
            isSequential: false
        )
        
        // 选择状态 - 序号模式
        UnifiedSelectionView(
            sound: "sound3", 
            items: ["sound2", "sound3"], 
            isSequential: true
        )
    }
    .padding()
    .background(AppTheme.backgroundColor)
} 