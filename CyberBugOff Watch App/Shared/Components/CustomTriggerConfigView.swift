import SwiftUI

struct CustomTriggerConfigView: View {
    @ObservedObject var model: BugOffModel
    let imageName: String
    @Binding var isPresented: Bool
    
    @State private var config: CustomTriggerDisplay

    // 多选颜色支持
    @State private var selectedColors: Set<String> = []
    @State private var colorChangeTimer: Timer? = nil
    @State private var currentColorIndex: Int = 0
    @State private var clickColorIndex: Int = 0 // 用于点击时切换颜色

    // 性能优化：缓存计算结果
    @State private var cachedColorArray: [String] = []
    @State private var lastSelectedColors: Set<String> = []
    
    @State private var showFontSizeSettings: Bool = false
    @State private var showColorSettings: Bool = false
    @State private var showAnimationStyleSettings: Bool = false
    
    init(model: BugOffModel, imageName: String, isPresented: Binding<Bool>) {
        self.model = model
        self.imageName = imageName
        self._isPresented = isPresented
        
        // 获取当前配置并确保启用状态和文字模式
        var currentConfig = model.getCustomTriggerDisplay(for: imageName)
        currentConfig.isEnabled = true // 默认启用
        currentConfig.displayMode = .text // 确保是文字模式
        self._config = State(initialValue: currentConfig)
        
        // 尝试从UserDefaults加载保存的颜色选择
        if let colorData = UserDefaults.standard.data(forKey: "selectedColors_\(imageName)"),
           let colors = try? JSONDecoder().decode([String].self, from: colorData) {
            self._selectedColors = State(initialValue: Set(colors))
        } else if currentConfig.displayColor != "white" {
            // 如果没有保存的颜色但有配置的颜色，使用配置的颜色
            self._selectedColors = State(initialValue: Set([currentConfig.displayColor]))
        } else {
            // 默认选择白色
            self._selectedColors = State(initialValue: Set(["white"]))
        }
    }

    // MARK: - 动画样式设置区域

    /// 动画样式设置区域
    private var animationStyleSection: some View {
        AnimationStyleSelectorView(
            selectedStyle: Binding(
                get: { config.getCurrentAnimationStyle() },
                set: { config.setCurrentAnimationStyle($0) }
            ),
            isExpanded: $showAnimationStyleSettings,
            onStyleChanged: {
                saveSettings()
            }
        )
    }

    var body: some View {
        NavigationView {
            ScrollView(.vertical, showsIndicators: true) {
                VStack(alignment: .leading, spacing: AppTheme.mediumPadding) {
                    // 自定义文本输入区域
                    CustomTextInputView(config: $config, saveAction: saveSettings)

                    // 随机配置按钮
                    randomConfigButton
                    
                    // 字体大小设置区域
                    VStack(alignment: .leading, spacing: AppTheme.smallPadding) {
                        // 字体大小标题行
                        Button(action: {
                            showFontSizeSettings.toggle()
                        }) {
                            StandardRowContent(
                                leftIcon: "textformat.size",
                                leftTitle: "字体大小",
                                rightText: config.fontSize == AppTheme.defaultFontSize ? "默认" : "\(Int(config.fontSize))",
                                isExpanded: showFontSizeSettings
                            )
                            .standardRowStyle()
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        // 字体大小滑块区域 - 只在展开时显示
                        if showFontSizeSettings {
                            fontSizeSliderView
                        }
                    }
                    
                    // 文字颜色设置
                    VStack(alignment: .leading, spacing: AppTheme.smallPadding) {
                        // 颜色标题行
                        Button(action: {
                            showColorSettings.toggle()
                        }) {
                            HStack(alignment: .center) {
                                StandardRowLeftContent(icon: "paintbrush.fill", title: "文字颜色")

                                Spacer()

                                // 右侧当前值显示 - 保持自定义颜色显示逻辑
                                HStack(spacing: AppTheme.smallPadding) {
                                    if config.displayColor == "rainbow" {
                                        Circle()
                                            .fill(
                                                AngularGradient(
                                                    gradient: Gradient(colors: [.red, .yellow, .green, .blue, .purple, .red]),
                                                    center: .center
                                                )
                                            )
                                            .frame(width: Sizes.smallIconSize, height: Sizes.smallIconSize)
                                            .overlay(
                                                Circle()
                                                    .stroke(Color.white, lineWidth: 1)
                                            )
                                    } else if selectedColors.count > 1 {
                                        Text("多色")
                                            .font(.appSmall)
                                            .foregroundColor(Color.gray)
                                    } else if selectedColors.isEmpty || config.displayColor == "white" {
                                        Circle()
                                            .fill(Color.white)
                                            .frame(width: Sizes.smallIconSize, height: Sizes.smallIconSize)
                                            .overlay(
                                                Circle()
                                                    .stroke(Color.gray, lineWidth: 1)
                                            )
                                    } else {
                                        Circle()
                                            .fill(AppTheme.getColor(fromName: config.displayColor))
                                            .frame(width: Sizes.smallIconSize, height: Sizes.smallIconSize)
                                            .overlay(
                                                Circle()
                                                    .stroke(Color.white, lineWidth: 1)
                                            )
                                    }

                                    Image(systemName: showColorSettings ? "chevron.up" : "chevron.down")
                                        .font(.appSmall)
                                        .foregroundColor(Color.gray)
                                }
                            }
                            .standardRowStyle()
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        // 颜色选择器 - 只在展开时显示
                        if showColorSettings {
                            colorPickerView
                        }
                    }
                    
                    // 显示增量/减量开关区域
                    Button(action: {
                        config.showIncrement.toggle()
                        saveSettings()
                    }) {
                        HStack(alignment: .center) {
                            StandardRowLeftContent(
                                icon: config.incrementValue >= 0 ? "plus.circle" : "minus.circle",
                                title: "计数"
                            )

                            Spacer()

                            // 右侧开关
                            Toggle("", isOn: $config.showIncrement)
                                .labelsHidden()
                                .allowsHitTesting(false) // 防止Toggle自身接收点击事件
                        }
                        .standardRowStyle()
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    // 增量减量值切换区域 - 只有当显示计数值开启时才显示
                    if config.showIncrement {
                        Button(action: {
                            // 在正数和负数之间切换
                            config.incrementValue = -config.incrementValue
                            saveSettings()
                        }) {
                            StandardRowContent(
                                leftIcon: config.incrementValue >= 0 ? "plus" : "minus",
                                leftTitle: "计数方向",
                                rightText: config.incrementValue >= 0 ? "增加" : "减少",
                                showChevron: false
                            )
                            .standardRowStyle()
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle())
                        .transition(.opacity.combined(with: .scale))
                    }

                    // 动画样式设置区域
                    animationStyleSection

                    // 预览区域
                    PreviewDisplayView(
                        config: config,
                        currentCount: model.getClickCount(for: imageName),
                        currentColor: getCurrentDisplayColor(),
                        selectedColors: Array(selectedColors)
                    )
                }
                .padding(.vertical)
            }
            .navigationTitle("自定义提示")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            loadSettings()
            // 如果是彩色模式，启动颜色切换定时器
            if config.displayColor == "rainbow" || selectedColors.count > 1 {
                startColorChangeTimer()
            }
        }
        .onDisappear {
            stopColorChangeTimer()
        }
    }
    
    // 字体大小滑块视图
    private var fontSizeSliderView: some View {
        VStack(spacing: AppTheme.smallPadding) {
            // 字体大小滑块
            Slider(
                value: $config.fontSize,
                in: AppTheme.minFontSize...AppTheme.maxFontSize,
                step: AppTheme.fontSizeStep
            ) {
                Text("字体大小")
            } minimumValueLabel: {
                Text("A")
                    .font(.appSmall)
            } maximumValueLabel: {
                Text("A")
                    .font(.appTitle)
            }
            .tint(AppTheme.primaryColor)
            .onChange(of: config.fontSize) { oldValue, newValue in
                // 实时保存设置，确保字体大小调整后立即生效
                saveSettings()
            }
        }
        .padding(.horizontal)
        .padding(.vertical, AppTheme.smallPadding)
        .cornerRadius(AppTheme.cornerRadius)
        .animation(.spring(response: 0.35, dampingFraction: 0.8), value: showFontSizeSettings)
    }
    
    // 颜色选择器视图
    private var colorPickerView: some View {
        VStack(spacing: AppTheme.mediumPadding) {
            // 颜色选项
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: AppTheme.mediumPadding) {
                    ForEach(AppTheme.colorOptions, id: \.name) { option in
                        Button(action: {
                            toggleColor(option.name)
                        }) {
                            if option.name == "rainbow" {
                                Circle()
                                    .fill(
                                        AngularGradient(
                                            gradient: Gradient(colors: [.red, .yellow, .green, .blue, .purple, .red]),
                                            center: .center
                                        )
                                    )
                                    .frame(width: Sizes.smallButtonHeight, height: Sizes.smallButtonHeight)
                                    .overlay(
                                        Circle()
                                            .stroke(Color.white, lineWidth: 1)
                                    )
                                    .overlay(
                                        Circle()
                                            .stroke(AppTheme.primaryColor, lineWidth: 3)
                                            .opacity(selectedColors.contains(option.name) ? 1 : 0)
                                    )
                                    .padding(Sizes.smallPadding)
                            } else {
                                Circle()
                                    .fill(option.color)
                                    .frame(width: Sizes.smallButtonHeight, height: Sizes.smallButtonHeight)
                                    .overlay(
                                        Circle()
                                            .stroke(Color.white, lineWidth: 1)
                                    )
                                    .overlay(
                                        Circle()
                                            .stroke(AppTheme.primaryColor, lineWidth: 3)
                                            .opacity(selectedColors.contains(option.name) ? 1 : 0)
                                    )
                                    .padding(Sizes.smallPadding)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.vertical, AppTheme.mediumPadding)
                .padding(.horizontal, AppTheme.smallPadding)
            }
            
            // 多选提示
            Text("提示：可以选择多种颜色，文字将会循环变色")
                .smallTextStyle()
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.horizontal, Sizes.tinyPadding)
        }
        .background(AppTheme.secondaryBackgroundColor.opacity(0.3))
        .cornerRadius(AppTheme.cornerRadius)
    }
    
    // 获取当前显示颜色
    private func getCurrentDisplayColor() -> Color {
        if selectedColors.isEmpty {
            // 没有选中颜色时返回默认颜色
            return .white
        } else if selectedColors.count == 1 {
            return AppTheme.getColor(fromName: selectedColors.first!)
        } else if selectedColors.contains("rainbow") {
            // 彩色模式特殊处理，这里简单返回一个颜色
            // 实际效果会在PreviewDisplayView中处理
            return .red
        } else {
            // 多选模式，返回当前索引对应的颜色
            if currentColorIndex < selectedColors.count {
                return AppTheme.getColor(fromName: Array(selectedColors)[currentColorIndex])
            }
            return .white
        }
    }
    
    // 切换颜色选择
    private func toggleColor(_ color: String) {
        // 如果是彩虹色模式，特殊处理
        if color == "rainbow" {
            if selectedColors.contains("rainbow") {
                // 如果已经选择了彩虹色，则移除
                selectedColors.remove("rainbow")
            } else {
                // 如果选择彩虹色，清除其他颜色
                selectedColors.removeAll()
                selectedColors.insert("rainbow")
            }
        } else {
            // 如果选择了普通颜色，移除彩虹色
            selectedColors.remove("rainbow")
            
            // 切换颜色选择状态
            if selectedColors.contains(color) {
                selectedColors.remove(color)
            } else {
                selectedColors.insert(color)
            }
        }
        
        // 确保至少有一个颜色被选中
        if selectedColors.isEmpty {
            selectedColors.insert("white") // 默认白色
        }
        
        // 保存设置
        saveSettings()
    }
    
    // 启动颜色切换定时器
    private func startColorChangeTimer() {
        // 先停止现有定时器
        stopColorChangeTimer()
        
        // 如果是彩色模式或多选模式，启动颜色切换定时器
        if selectedColors.contains("rainbow") || selectedColors.count > 1 {
            colorChangeTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
                if selectedColors.count > 1 {
                    // 多色模式
                    currentColorIndex = (currentColorIndex + 1) % selectedColors.count
                } else if config.displayColor == "rainbow" {
                    // 彩色模式特殊处理
                    currentColorIndex = (currentColorIndex + 1) % 6 // 使用6种基本颜色循环
                }
            }
        }
    }
    
    // 停止颜色切换定时器
    private func stopColorChangeTimer() {
        colorChangeTimer?.invalidate()
        colorChangeTimer = nil
    }
    
    private func loadSettings() {
        config = model.getCustomTriggerDisplay(for: imageName)
        
        // 确保启用状态
        config.isEnabled = true
        
        // 尝试从UserDefaults加载保存的颜色选择
        if let colorData = UserDefaults.standard.data(forKey: "selectedColors_\(imageName)") {
            if let colors = try? JSONDecoder().decode([String].self, from: colorData) {
                selectedColors = Set(colors)
            } else {
                // 初始化选中颜色
                selectedColors = Set([config.displayColor])
            }
        } else {
            // 初始化选中颜色
            selectedColors = Set([config.displayColor])
        }
        
        // 如果是彩色模式或多选模式，启动颜色切换定时器
        if config.displayColor == "rainbow" || selectedColors.count > 1 {
            startColorChangeTimer()
        }
    }

    private func saveSettings() {
        // 确保启用状态和文字模式
        config.isEnabled = true
        config.displayMode = .text // 确保保存时是文字模式

        // 清空emoji字段，因为现在包含在自定义文本中
        config.emoji = ""
        
        // 保存多选颜色信息
        if selectedColors.count > 0 {
            // 使用第一个颜色作为基础颜色
            config.displayColor = selectedColors.first!
        }
        
        model.setCustomTriggerDisplay(for: imageName, config: config)
        
        // 更新多选颜色状态
        if let colorData = try? JSONEncoder().encode(Array(selectedColors)) {
            UserDefaults.standard.set(colorData, forKey: "selectedColors_\(imageName)")
        }
    }
    
    // 增加字体大小
    private func increaseFontSize() {
        config.fontSize = min(config.fontSize + AppTheme.quickFontSizeStep, AppTheme.maxFontSize)
        config.fontSize = round(config.fontSize) // 确保是整数
        saveSettings()
    }
    
    // 减小字体大小
    private func decreaseFontSize() {
        config.fontSize = max(config.fontSize - AppTheme.quickFontSizeStep, AppTheme.minFontSize)
        config.fontSize = round(config.fontSize) // 确保是整数
        saveSettings()
    }
    
    // 触发图片时调用此方法
    func triggerWithColorChange() {
        // 先触发图片
        model.triggerImage(for: imageName)
        
        // 如果是多色模式，更新当前颜色索引
        if selectedColors.count > 1 {
            // 重置模型中的颜色索引以确保正确的循环
            model.resetColorIndex(for: imageName)
            
            // 获取并递增颜色索引
            clickColorIndex = model.getAndIncrementColorIndex(for: imageName) % selectedColors.count
            
            // 更新UI
            DispatchQueue.main.async {
                currentColorIndex = clickColorIndex
            }
        }
    }

    // MARK: - 随机配置按钮
    private var randomConfigButton: some View {
        Button(action: randomizeConfiguration) {
            StandardRowContent(
                leftIcon: "shuffle",
                leftTitle: "随机配置",
                showChevron: false
            )
            .standardRowStyle()
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 随机配置方法
    private func randomizeConfiguration() {
        // 随机选择自定义文本（从全局配置中选择）
        config.customText = AppConfig.randomTextPresets.randomElement() ?? "太棒了!"

        // 根据文案末尾是否为标点符号决定是否显示增量
        config.showIncrement = shouldShowIncrementForText(config.customText)

        // 随机选择增量值（使用加权随机，小数值概率更高）
        config.incrementValue = generateWeightedRandomIncrement()

        // 随机选择字体大小（从可选范围中随机选择）
        config.fontSize = Double.random(in: AppConfig.fontSizeRange)

        // 随机选择动画样式
        config.setCurrentAnimationStyle(TriggerAnimationStyle.allCases.randomElement() ?? .bounce)

        // 随机选择颜色配置
        randomizeColors()

        // 保存配置
        saveSettings()

        // 触觉反馈
        WKInterfaceDevice.current().play(.click)

        // 重新启动颜色定时器（如果需要）
        if config.displayColor == "rainbow" || selectedColors.count > 1 {
            startColorChangeTimer()
        }
    }

    // 判断是否应该显示增量（基于文案末尾字符）
    private func shouldShowIncrementForText(_ text: String) -> Bool {
        guard !text.isEmpty else { return Bool.random() }

        // 定义标点符号集合
        let punctuationMarks: Set<Character> = ["!", "。", "？", "?", "！", "…", "~", "～", ".", ",", "，", "；", ";", ":", "："]

        // 获取文案的最后一个字符
        let lastCharacter = text.last!

        // 如果末尾是标点符号，不显示增量
        if punctuationMarks.contains(lastCharacter) {
            return false
        }

        // 如果末尾不是标点符号，随机决定是否显示增量
        return Bool.random()
    }

    // 生成加权随机增量值（小数值概率更高，排除0）
    private func generateWeightedRandomIncrement() -> Int {
        var finalValue: Int

        repeat {
            // 使用指数分布来生成加权随机数
            let random = Double.random(in: 0...1)

            // 使用指数函数，让小数值有更高的概率
            let weightedRandom = pow(random, 3)

            // 映射到范围 [1, maxAbsValue]，避免生成0
            let maxAbsValue = Double(AppConfig.incrementValueRange.upperBound)
            let scaledValue = max(1, weightedRandom * maxAbsValue)

            // 随机决定正负
            let isPositive = Bool.random()
            finalValue = isPositive ? Int(scaledValue) : -Int(scaledValue)

            // 确保在范围内且不为0
            finalValue = max(AppConfig.incrementValueRange.lowerBound,
                           min(AppConfig.incrementValueRange.upperBound, finalValue))
        } while finalValue == 0

        return finalValue
    }

    // 随机选择颜色配置
    private func randomizeColors() {
        let availableColors = AppConfig.defaultColors
        let colorModes = ["single", "multiple", "rainbow"]
        let selectedMode = colorModes.randomElement() ?? "single"

        switch selectedMode {
        case "single":
            // 单色模式：随机选择一个颜色
            let randomColor = availableColors.randomElement() ?? "white"
            config.displayColor = randomColor
            selectedColors = Set([randomColor])

        case "multiple":
            // 多色模式：随机选择2-4个颜色
            let colorCount = Int.random(in: 2...min(4, availableColors.count))
            let shuffledColors = availableColors.shuffled()
            let randomColors = Array(shuffledColors.prefix(colorCount))
            selectedColors = Set(randomColors)
            config.displayColor = randomColors.first ?? "white"

        case "rainbow":
            // 彩虹模式
            config.displayColor = "rainbow"
            selectedColors = Set(["rainbow"])

        default:
            break
        }

        // 保存颜色选择
        if let colorData = try? JSONEncoder().encode(Array(selectedColors)) {
            UserDefaults.standard.set(colorData, forKey: "selectedColors_\(imageName)")
        }
    }
}

// MARK: - 子视图

// 自定义文本输入视图
struct CustomTextInputView: View {
    @Binding var config: CustomTriggerDisplay
    let saveAction: () -> Void

    var body: some View {
        StandardTextField(
            placeholder: "输入自定义文本",
            text: $config.customText,
            limit: AppConfig.maxSoundNameLength,
            removeWhitespace: false,
            onTextChange: { _ in
                saveAction()
            }
        )
    }
}

// 预览显示视图
struct PreviewDisplayView: View {
    let config: CustomTriggerDisplay
    let currentCount: Int
    let currentColor: Color
    let selectedColors: [String]
    
    // 彩色模式支持
    @State private var rainbowColorIndex: Int = 0
    @State private var multiColorIndex: Int = 0
    @State private var colorTimer: Timer? = nil
    
    private let rainbowColors: [Color] = [.red, .orange, .yellow, .green, .blue, .purple]
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.smallPadding) {
            Text("预览效果")
                .font(.appSmall)
                .foregroundColor(Color.textPrimary)
                .padding(.horizontal)
            
            HStack {
                Spacer()
                Text(getDisplayText())
                    .font(.system(size: AppTheme.adaptiveSize(config.fontSize), weight: .bold))
                    .foregroundColor(getDisplayColor())
                    .padding(AppTheme.mediumPadding)
                    .background(AppTheme.secondaryBackgroundColor)
                    .cornerRadius(AppTheme.cornerRadius)
                Spacer()
            }
        }
        .onAppear {
            startColorTimer()
        }
        .onChange(of: currentColor) { _, _ in
            // 当当前颜色变化时，重新启动定时器
            startColorTimer()
        }
        .onChange(of: selectedColors) { _, _ in
            // 当选择的颜色列表变化时，重新启动定时器
            startColorTimer()
        }
        .onDisappear {
            stopColorTimer()
        }
    }
    
    private func getDisplayText() -> String {
        if config.showIncrement {
            // 显示计数值模式：使用全局配置中设置的incrementValue
            let configValue = abs(config.incrementValue) // 取绝对值，符号由incrementValue的正负决定
            let sign = config.incrementValue >= 0 ? "+" : "-"
            let displayValue = "\(sign)\(configValue)"
            
            if config.customText.isEmpty {
                return displayValue
            } else {
                return "\(config.customText) \(displayValue)"
            }
        } else {
            // 不显示计数值模式：只显示自定义文案，如果为空则显示提示文本
            return config.customText.isEmpty ? "自定义文案" : config.customText
        }
    }
    
    private func getDisplayColor() -> Color {
        if config.displayColor == "rainbow" {
            // 确保索引在有效范围内
            let safeIndex = min(rainbowColorIndex, rainbowColors.count - 1)
            return rainbowColors[safeIndex]
        } else if selectedColors.count > 1 {
            // 多色模式，确保索引在有效范围内
            if !selectedColors.isEmpty && multiColorIndex < selectedColors.count {
                return AppTheme.getColor(fromName: selectedColors[multiColorIndex])
            }
            return .white // 默认颜色
        }
        return currentColor
    }
    
    private func startColorTimer() {
        // 先停止现有定时器
        stopColorTimer()
        
        // 如果是彩色模式或多色模式，启动颜色切换定时器
        if config.displayColor == "rainbow" || selectedColors.count > 1 {
            colorTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
                if config.displayColor == "rainbow" {
                    // 彩色模式，确保索引在有效范围内
                    rainbowColorIndex = (rainbowColorIndex + 1) % max(1, rainbowColors.count)
                } else if !selectedColors.isEmpty {
                    // 多色模式，确保索引在有效范围内
                    multiColorIndex = (multiColorIndex + 1) % max(1, selectedColors.count)
                }
            }
        }
    }
    
    private func stopColorTimer() {
        if colorTimer != nil {
            colorTimer?.invalidate()
            colorTimer = nil
        }
    }
}

#Preview {
    CustomTriggerConfigView(
        model: BugOffModel(),
        imageName: "bug1",
        isPresented: .constant(true)
    )
}



