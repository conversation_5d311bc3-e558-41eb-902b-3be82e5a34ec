import SwiftUI
import CoreMotion

struct FullScreenImageView: View {
    let defaultImageName: String
    @Binding var isPresented: Bool
    @EnvironmentObject private var imageManager: ImageManager
    @EnvironmentObject private var soundManager: SoundManager
    @EnvironmentObject private var triggerManager: TriggerManager

    // 兼容旧代码：保留 model 但弱引用，仅在开关关闭时使用
    var model: BugOffModel

    private var useDirect: Bool { AppConfig.useDirectManagerBinding }

    // 当前正在显示的 mode 名称，实时读取模型中的选中值
    private var currentImageName: String { model.selectedDefaultImageName }

    @State private var showingAddSoundSheet = false
    @State private var selectedSounds: Set<String> = Set<String>()
    @State private var showingSettings = false
    @State private var clickCount = 0
    @State private var selectedSoundForList: String? = nil
    
    // 控制回溯按钮的显示
    @State private var showBacktrackButton = false

    // 添加摇晃检测
    @State private var isShaking = false

    // 添加触发次数显示管理器
    @StateObject private var triggerCountManager = TriggerCountToastManager()

    // 添加图片触发动画管理器
    @StateObject var animationManager = ImageTriggerAnimationManager()

    // 调试状态
    @State private var isDebugMode = false
    
    // 预加载状态跟踪，防止重复预加载
    @State private var preloadedImages: Set<String> = []

    // 添加圈选裁剪图片缓存，避免每次点击都重新计算
    @State private var cachedCircleSelectionImage: UIImage? = nil
    @State private var lastCircleSelectionCacheKey: String = ""

    // 添加 CoreMotion 管理器
    private let motionManager = CMMotionManager()
    private let shakeThreshold: Double = 1.5 // 摇晃阈值

    // 自动触发定时器
    @State private var autoTriggerTimer: Timer?

    // MARK: - Mode Navigation Support
    // 移除多图片相关状态，现在用于mode切换
    
    var body: some View {
        ZStack {
            // Layer 0: 黑色背景，防止圈选裁剪的透明区域透出底层Grid视图
            Color.black
                .edgesIgnoringSafeArea(.all)
            
            // Layer 1: Content Area (Image + Toast Container)
            // This ZStack contains the main interactive content.
            // 现在总是使用自定义Toast显示，不再依赖于"显示次数"开关
            CustomClickableToastView(
                toastManager: triggerCountManager,
                config: model.getCustomTriggerDisplay(for: currentImageName),
                currentCount: model.getClickCount(for: currentImageName),
                imageManager: imageManager,
                imageName: currentImageName,
                triggerManager: model.triggerManager,
                isDebugMode: isDebugMode,
                onTap: { location in
                    if model.getTriggerMode(for: currentImageName) == .tap {
                        handleTrigger(at: location)
                    }
                }
            ) {
                imageContent
            }
        }
        .edgesIgnoringSafeArea(.all) // Ensure the content ZStack fills the entire screen
        .overlay(alignment: .topTrailing) {
            // 临时调试区域 - 双击清理Toast缓存，三击切换调试模式
            Rectangle()
                .fill(Color.clear)
                .frame(width: 50, height: 50)
                .onTapGesture(count: 2) {
                    print("🔧 用户触发调试：清理所有缓存")
                    TriggerManager.clearToastImageCache()
                    model.triggerManager.refreshToastImageCache(for: currentImageName)
                    
                    // 清理圈选裁剪图片缓存
                    invalidateCircleSelectionCache()
                    
                    // 清理预加载状态，允许重新预加载
                    preloadedImages.removeAll()
                    
                    // 触发轻微震动反馈
                    WKInterfaceDevice.current().play(.click)
                }
                .onTapGesture(count: 3) {
                    isDebugMode.toggle()
                    print("🚨 调试模式切换: \(isDebugMode ? "开启" : "关闭")")
                    
                    // 触发强震动反馈
                    WKInterfaceDevice.current().play(.success)
                }
                .padding(.top, 10)
                .padding(.trailing, 10)
        }
        .overlay(alignment: .bottomTrailing) {
            // Layer 2: Settings Button - Beautified and Centered
            // Placed in an overlay to be independent of all other content.
            Button(action: {
                // 在显示设置视图之前停止所有音效和自动触发
                model.stopSound()
                stopAutoTrigger()
                showingSettings = true
            }) {
                ZStack {
                    // Frosted glass background for a modern look
                    Circle()
                        .fill(.clear)
                        .frame(width: AppTheme.adaptiveSize(40), height: AppTheme.adaptiveSize(40))
                        .background(.ultraThinMaterial, in: Circle())

                    Image(systemName: "gearshape.fill")
                        .font(.system(size: AppTheme.adaptiveSize(26)))
                        .foregroundColor(.white)
                }
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.bottom, AppTheme.adaptiveSize(20))
            .padding(.trailing, AppTheme.adaptiveSize(15))
        }
        .overlay(alignment: .bottomLeading) {
            // 回溯按钮
            if showBacktrackButton {
                Button(action: handleBacktrack) {
                    ZStack {
                        Circle()
                            .fill(.clear)
                            .frame(width: AppTheme.adaptiveSize(40), height: AppTheme.adaptiveSize(40))
                            .background(.ultraThinMaterial, in: Circle())

                        Image(systemName: "arrow.trianglehead.counterclockwise")
                            .font(.system(size: AppTheme.adaptiveSize(22)))
                            .foregroundColor(.white)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.bottom, AppTheme.adaptiveSize(20))
                .padding(.leading, AppTheme.adaptiveSize(15))
                .transition(.asymmetric(
                    insertion: .scale.combined(with: .opacity).animation(.easeOut(duration: 0.2)),
                    removal: .scale.combined(with: .opacity).animation(.easeIn(duration: 0.15))
                ))
            }
        }
        // 移除多图片导航指示器，现在用于mode切换
        // 移除左右mode切换按钮，只保留滑动手势
        .sheet(isPresented: $showingSettings, onDismiss: {
            // 设置视图关闭后，如果当前模式是自动触发，重新启动自动触发
            if model.getTriggerMode(for: currentImageName) == .auto {
                startAutoTrigger()
            }
            
            // 设置视图关闭后，更新圈选裁剪缓存
            invalidateCircleSelectionCache()
            preloadCircleSelectionImage()
        }) {
            ImageSettingsView(
                model: model,
                imageName: currentImageName,
                isPresented: $showingSettings
            )
        }
        .highPriorityGesture(
            DragGesture(minimumDistance: 20)
                .onEnded { gesture in
                    // 垂直滑动：关闭视图
                    if abs(gesture.translation.height) > abs(gesture.translation.width) && gesture.translation.height > 50 {
                        isPresented = false
                    }
                    // 水平滑动：切换上一个、下一个mode
                    else if abs(gesture.translation.width) > abs(gesture.translation.height) {
                        if gesture.translation.width > 50 {
                            // 向右滑动：上一个mode
                            if let previousModeName = model.previousMode(from: currentImageName) {
                                // 立即停止当前播放的音效
                                model.stopSound()
                                model.selectedDefaultImageName = previousModeName
                                // 触发图片切换动画
                                animationManager.triggerAnimation()
                                // 立即更新回溯按钮显示状态
                                updateBacktrackButtonVisibility()
                            }
                        } else if gesture.translation.width < -50 {
                            // 向左滑动：下一个mode
                            if let nextModeName = model.nextMode(from: currentImageName) {
                                // 立即停止当前播放的音效
                                model.stopSound()
                                model.selectedDefaultImageName = nextModeName
                                // 触发图片切换动画
                                animationManager.triggerAnimation()
                                // 立即更新回溯按钮显示状态
                                updateBacktrackButtonVisibility()
                            }
                        }
                    }
                }
        )
        .onLongPressGesture(minimumDuration: 0.5) {
            isPresented = false
        }
        .onAppear {
            // 加载点击次数
            clickCount = model.getClickCount(for: currentImageName)

            // 如果是自动播放模式，启动自动触发定时器
            if model.getTriggerMode(for: currentImageName) == .auto {
                startAutoTrigger()
            }

            // 如果是摇晃模式，启动摇晃检测
            if model.getTriggerMode(for: currentImageName) == .shake {
                startShakeDetection()
            }

            // 检查是否有音效配置，决定是否显示回溯按钮
            updateBacktrackButtonVisibility()
            
            // 预加载当前图片的Toast版本以提升性能（避免重复）
            if !preloadedImages.contains(currentImageName) {
                model.triggerManager.preloadCustomDisplayImage(for: currentImageName)
                preloadedImages.insert(currentImageName)
            }
            
            // 预加载相邻图片的Toast版本（避免重复）
            if let nextImageName = model.nextMode(from: currentImageName),
               !preloadedImages.contains(nextImageName) {
                model.triggerManager.preloadCustomDisplayImage(for: nextImageName)
                preloadedImages.insert(nextImageName)
            }
            if let prevImageName = model.previousMode(from: currentImageName),
               !preloadedImages.contains(prevImageName) {
                model.triggerManager.preloadCustomDisplayImage(for: prevImageName)
                preloadedImages.insert(prevImageName)
            }
            
            // 预加载当前圈选裁剪图片到缓存
            preloadCircleSelectionImage()
        }
        .onChange(of: currentImageName) { _, newImageName in
            // 当切换图片时，清理旧的圈选裁剪缓存
            invalidateCircleSelectionCache()
            
            // 预加载新图片的圈选裁剪图片
            preloadCircleSelectionImage()
            
            // 更新其他状态
            clickCount = model.getClickCount(for: newImageName)
            updateBacktrackButtonVisibility()

            // 如果是自动播放模式，重新启动自动触发定时器
            if model.getTriggerMode(for: newImageName) == .auto {
                startAutoTrigger()
            } else {
                stopAutoTrigger()
            }
            
            // 如果是摇晃模式，启动摇晃检测；否则停止
            if model.getTriggerMode(for: newImageName) == .shake {
                startShakeDetection()
            } else {
                stopShakeDetection()
            }
        }
        .onChange(of: model.imageMultiSounds) {  _, _ in
            // 当音效配置改变时，立即更新回溯按钮显示状态
            updateBacktrackButtonVisibility()
        }
        .onReceive(model.objectWillChange) { _ in
            // 当ImageManager中的设置发生变化时（包括enableBacktrack），立即更新回溯按钮显示状态
            updateBacktrackButtonVisibility()
        }
        // 监听 imageManager 的变化（如 enableBacktrack 开关），实时更新回溯按钮
        .onReceive(imageManager.objectWillChange) { _ in
            updateBacktrackButtonVisibility()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("AutoTriggerIntervalChanged"))) { notification in
            // 监听自动触发时间间隔变化通知
            // 只有在设置视图未显示时才响应（避免在设置界面调整时触发）
            if !showingSettings,
               let userInfo = notification.userInfo,
               let imageName = userInfo["imageName"] as? String,
               imageName == currentImageName,
               model.getTriggerMode(for: currentImageName) == .auto {
                // 重新启动自动触发定时器
                startAutoTrigger()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("TriggerModeChanged"))) { notification in
            // 监听触发模式变化通知
            // 只有在设置视图未显示时才响应（避免在设置界面切换时触发）
            if !showingSettings,
               let userInfo = notification.userInfo,
               let imageName = userInfo["imageName"] as? String,
               imageName == currentImageName {
                // 先停止当前的自动触发定时器
                stopAutoTrigger()

                // 如果新模式是自动触发，则启动定时器
                if model.getTriggerMode(for: currentImageName) == .auto {
                    startAutoTrigger()
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("CircleSelectionUpdated"))) { notification in
            // 监听圈选裁剪数据更新通知
            if let userInfo = notification.userInfo,
               let imageName = userInfo["imageName"] as? String,
               imageName == currentImageName {
                print("📱 收到圈选裁剪更新通知，重新加载缓存")
                // 清理圈选裁剪缓存
                invalidateCircleSelectionCache()
                // 重新加载圈选裁剪图片
                preloadCircleSelectionImage()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ImageSizeUpdated"))) { notification in
            // 监听图片大小调整通知
            if let userInfo = notification.userInfo,
               let imageName = userInfo["imageName"] as? String,
               imageName == currentImageName {
                print("📱 收到图片大小更新通知，刷新显示")
                // 触发视图刷新以应用新的缩放比例
                // 由于getCurrentImageScale()会读取最新的设置，这里只需要触发重新渲染
            }
        }
        .onDisappear {
            // 停止摇晃检测
            stopShakeDetection()
            // 停止自动触发定时器
            stopAutoTrigger()
            // 停止所有音效及计划音效
            model.stopSound()
        }
        .sheet(isPresented: $showingAddSoundSheet) {
            NavigationStack {
                SoundListView(
                    model: model,
                    mode: .multiSelect,
                    selectedSound: $selectedSoundForList,
                    selectedSounds: $selectedSounds,
                    imageName: currentImageName
                )
            }
        }
        // 移除定时器，回溯按钮现在根据音效配置直接显示/隐藏
    }
    
    // 更新回溯按钮的显示状态
    private func updateBacktrackButtonVisibility() {
        // 使用当前选中的mode名称
        let currentModeName = model.selectedDefaultImageName

        // 确定正确的上下文
        let modeContext: ModeContext
        if currentModeName.contains("_copy_") {
            modeContext = ModeContext(modeId: currentModeName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        // 获取图片设置，检查是否启用回溯功能，使用正确的上下文
        let settings = imageManager.getImageSettings(for: currentModeName, in: modeContext)
        let isBacktrackEnabled = settings.enableBacktrack

        // 检查当前mode是否有音效配置，只要有音效配置且启用回溯功能就显示回溯按钮
        let shouldShow: Bool
        if let sounds = model.imageMultiSounds[currentModeName], !sounds.isEmpty && isBacktrackEnabled {
            shouldShow = true
        } else {
            shouldShow = false
        }

        print("🔄 updateBacktrackButtonVisibility - currentModeName: \(currentModeName), 上下文: \(modeContext), enableBacktrack: \(isBacktrackEnabled), 音效: \(model.imageMultiSounds[currentModeName] ?? []), shouldShow: \(shouldShow)")

        // 只有当状态真正改变时才使用动画
        if shouldShow != showBacktrackButton {
            withAnimation(.easeInOut(duration: 0.2)) {
                showBacktrackButton = shouldShow
            }
        }
    }
    
    // 统一的触发处理方法 - 只负责播放和重新播放
    private func handleTrigger(at location: CGPoint?) {
        // 执行图片触发动画
        animationManager.triggerAnimation()

        // 停止当前播放的音效（如果有）
        model.stopSound()

        // 检查是否启用随机提示或自定义提示，使用正确的上下文
        let modeContext: ModeContext
        if currentImageName.contains("_copy_") {
            modeContext = ModeContext(modeId: currentImageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }
        let settings = imageManager.getImageSettings(for: currentImageName, in: modeContext)
        print("🎯 使用上下文: \(modeContext) 获取配置")

        print("🎯 触发Toast - currentImageName: \(currentImageName), randomHintEnabled: \(settings.randomHintEnabled)")

        // 如果当前不是随机提示模式，清除之前可能缓存的随机配置
        if !settings.randomHintEnabled {
            triggerCountManager.clearCustomConfig()
        }

        if settings.randomHintEnabled {
            // 使用随机提示 - 先生成随机配置
            let randomConfig = model.triggerManager.generateRandomTriggerDisplay()
            print("🎯 随机提示配置 - customText: '\(randomConfig.customText)', showIncrement: \(randomConfig.showIncrement), incrementValue: \(randomConfig.incrementValue)")

            // 使用随机配置的增量值来更新触发次数
            model.triggerManager.triggerImageWithCustomIncrement(
                for: currentImageName,
                incrementValue: randomConfig.incrementValue,
                imageManager: imageManager,
                soundManager: model.soundManager
            )

            let colorList = [AppTheme.getColor(fromName: randomConfig.displayColor)]

            // 对于随机提示，使用配置中的增量值作为显示计数
            let displayCount = randomConfig.incrementValue
            print("🎯 随机提示显示 - displayCount: \(displayCount), colors: \(colorList)")

            if let location = location {
                triggerCountManager.showCustomTrigger(
                    count: displayCount,
                    config: randomConfig,
                    colors: colorList,
                    at: location
                )
            } else {
                triggerCountManager.showCustomTrigger(
                    count: displayCount,
                    config: randomConfig,
                    colors: colorList
                )
            }
        } else {
            // 使用自定义提示（原有逻辑）
            model.triggerImage(for: currentImageName)

            let config = model.getCustomTriggerDisplay(for: currentImageName)
            print("🎯 自定义提示配置 - isEnabled: \(config.isEnabled), customText: '\(config.customText)', showIncrement: \(config.showIncrement), incrementValue: \(config.incrementValue)")

            if config.isEnabled {
                let colorList = getColorList(for: currentImageName, config: config)
                print("🎯 自定义提示显示 - clickCount: \(clickCount), colors: \(colorList)")

                if let location = location {
                    triggerCountManager.showCustomTrigger(
                        count: clickCount,
                        config: config,
                        colors: colorList,
                        at: location
                    )
                } else {
                    triggerCountManager.showCustomTrigger(
                        count: clickCount,
                        config: config,
                        colors: colorList
                    )
                }
            } else {
                // 使用传统模式
                print("🎯 传统模式显示 - clickCount: \(clickCount)")
                if let location = location {
                    triggerCountManager.showTriggerCount(clickCount, at: location)
                } else {
                    triggerCountManager.showTriggerCount(clickCount)
                }
            }
        }

        // 更新本地点击次数显示
        clickCount = model.getClickCount(for: currentImageName)
        
        // 更新回溯按钮显示状态
        updateBacktrackButtonVisibility()
    }
    
    // 专门处理回溯逻辑的方法
    private func handleBacktrack() {
        // 使用当前选中的mode名称
        let currentModeName = model.selectedDefaultImageName

        // 检查当前mode是否有音效配置
        guard let sounds = model.imageMultiSounds[currentModeName], !sounds.isEmpty else {
            return
        }

        // 执行图片触发动画
        animationManager.triggerAnimation()

        // 音效处理：回溯或播放
        if soundManager.isPlaying() {
            // 只进行音效回溯，不触发其他逻辑
            soundManager.backtrackCurrentSound()
        } else {
            // 如果没有音效在播放，播放当前mode的音效
            model.playMultiSounds(names: sounds, for: currentModeName)
        }

        // 异步处理自定义显示，避免阻塞主线程
        DispatchQueue.main.async {
            handleBacktrackTriggerDisplay()
        }

        // 更新回溯按钮显示状态
        updateBacktrackButtonVisibility()
    }

    // 处理回溯触发的自定义显示
    private func handleBacktrackTriggerDisplay() {
        // 检查是否启用随机提示或自定义提示
        let settings = imageManager.getImageSettings(for: currentImageName)

        // 如果当前不是随机提示模式，清除之前可能缓存的随机配置
        if !settings.randomHintEnabled {
            triggerCountManager.clearCustomConfig()
        }

        if settings.randomHintEnabled {
            // 使用随机提示 - 先生成随机配置
            let randomConfig = model.triggerManager.generateRandomTriggerDisplay()

            // 使用随机配置的增量值来更新触发次数
            model.triggerManager.triggerImageWithCustomIncrement(
                for: currentImageName,
                incrementValue: randomConfig.incrementValue,
                imageManager: imageManager,
                soundManager: model.soundManager
            )

            let colorList = [AppTheme.getColor(fromName: randomConfig.displayColor)]

            // 对于随机提示，使用配置中的增量值作为显示计数
            let displayCount = randomConfig.incrementValue

            // 使用回溯专用方法显示Toast（在上一次显示的位置）
            triggerCountManager.showBacktrackTrigger(
                count: displayCount,
                config: randomConfig,
                colors: colorList
            )
        } else {
            // 使用自定义提示（原有逻辑）
            model.triggerImage(for: currentImageName)

            let config = model.getCustomTriggerDisplay(for: currentImageName)
            if config.isEnabled {
                let colorList = getColorList(for: currentImageName, config: config)

                // 使用回溯专用方法显示Toast（在上一次显示的位置）
                triggerCountManager.showBacktrackTrigger(
                    count: clickCount,
                    config: config,
                    colors: colorList
                )
            }
        }

        // 更新本地点击次数显示
        clickCount = model.getClickCount(for: currentImageName)
    }

    // 提取颜色列表获取逻辑，避免重复代码
    private func getColorList(for imageName: String, config: CustomTriggerDisplay) -> [Color] {
        var colorList: [Color] = [.white] // 默认至少有一个白色

        if let colorData = UserDefaults.standard.data(forKey: "selectedColors_\(imageName)") {
            if let selectedColors = try? JSONDecoder().decode([String].self, from: colorData) {
                if !selectedColors.isEmpty { // 确保选择的颜色列表不为空
                    // 如果是彩虹色模式
                    if selectedColors.contains("rainbow") {
                        colorList = [.red, .orange, .yellow, .green, .blue, .purple]
                    }
                    // 如果是多色模式
                    else if selectedColors.count > 1 {
                        colorList = selectedColors.map { AppTheme.getColor(fromName: $0) }
                    }
                    // 单色模式
                    else if selectedColors.count == 1 {
                        colorList = [AppTheme.getColor(fromName: selectedColors[0])]
                    }
                }
            }
        } else {
            // 默认使用配置中的颜色，确保至少有一个颜色
            let configColor = config.getColor()
            colorList = [configColor]
        }

        // 确保颜色列表不为空
        if colorList.isEmpty {
            colorList = [.white] // 保底使用白色
        }

        return colorList
    }

    private func playImageSounds() {
        let names = model.imageMultiSounds[currentImageName] ?? []
        print("🎵 FullScreenImageView.playImageSounds - currentImageName: \(currentImageName), 音效列表: \(names)")
        if !names.isEmpty {
            model.playMultiSounds(names: names, for: currentImageName)
        } else {
            print("🎵 FullScreenImageView: 没有找到音效配置: \(currentImageName)")
        }
    }
    
    private func loadSelectedSounds() {
        selectedSounds.removeAll()
        if let names = model.imageMultiSounds[currentImageName] {
            selectedSounds = Set(names)
        }
    }
    
    // 摇晃检测相关方法
    private func startShakeDetection() {
        guard motionManager.isAccelerometerAvailable else {
            return
        }
        
        motionManager.accelerometerUpdateInterval = 0.1
        motionManager.startAccelerometerUpdates(to: .main) { data, error in
            guard let data = data, error == nil else {
                return
            }
            
            // 计算加速度向量的大小
            let acceleration = sqrt(pow(data.acceleration.x, 2) +
                                   pow(data.acceleration.y, 2) +
                                   pow(data.acceleration.z, 2))
            
            // 检测是否超过摇晃阈值
            if acceleration > self.shakeThreshold {
                // 只有在摇晃触发模式下才响应摇晃，与点击触发保持一致的条件
                if self.model.getTriggerMode(for: self.currentImageName) == .shake && !self.isShaking {
                    self.isShaking = true

                    // 在主线程上执行UI更新
                    DispatchQueue.main.async {
                        self.handleTrigger(at: nil)

                        // 1秒后重置摇晃状态，避免连续触发
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            self.isShaking = false
                        }
                    }
                }
            }
        }
    }
    
    private func stopShakeDetection() {
        if motionManager.isAccelerometerActive {
            motionManager.stopAccelerometerUpdates()
        }
    }

    // MARK: - Mode Navigation Methods
    // 多图片切换相关方法已移除，现在通过其他方式切换多图
}

extension FullScreenImageView {
    /// 承载图片本身的内容视图（包含裁剪/缩放等逻辑）
     var imageContent: some View {
        Group {
            // 显示当前mode的图片
            if let circleImage = getCircleSelectionImage() {
                // 圈选裁剪后的图片使用 scaledToFit 以确保完整显示，但仍然应用用户设置的图片大小和位置
                Image(uiImage: circleImage)
                    .resizable()
                    .scaledToFit()
                    // 应用用户设置的图片大小缩放和位置偏移
                    .scaleEffect(getCurrentImageScale())
                    .offset(getCurrentImageOffset())
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let uiImage = getCurrentDisplayImage() {
                // 普通图片使用 scaledToFill 以填充屏幕
                Image(uiImage: uiImage)
                    .resizable()
                    .scaledToFill()
                    // 应用缩放和偏移
                    .scaleEffect(getCurrentImageScale())
                    .offset(getCurrentImageOffset())
            } else {
                // 加载失败时占位
                Image(systemName: "photo")
                    .resizable()
                    .scaledToFit()
                    .foregroundColor(.gray)
            }
        }
        .frame(width: WKInterfaceDevice.current().screenBounds.width,
               height: WKInterfaceDevice.current().screenBounds.height)
        .clipped()
        .edgesIgnoringSafeArea(.all)
        .contentShape(Rectangle())
        .scaleEffect(animationManager.scale)
    }

    // MARK: - Image Helper Methods

    /// 获取应用了圈选裁剪的图片（如果有）
    private func getCircleSelectionImage() -> UIImage? {
        // 检查缓存
        if let cachedImage = cachedCircleSelectionImage, lastCircleSelectionCacheKey == currentImageName {
            return cachedImage
        }

        // 获取当前图片的自定义触发显示配置
        let config = model.getCustomTriggerDisplay(for: currentImageName)

        // 检查是否有主图圈选数据
        guard let selectionData = config.mainCircleSelectionData,
              !selectionData.pathPoints.isEmpty else {
            return nil
        }
        
        // 获取原始图片
        guard let originalImage = model.imageManager.getOriginalImage(for: currentImageName) else {
            return nil
        }

        // 应用圈选裁剪
        let renderedImage = model.applyCircleSelectionToImage(
            originalImage,
            selectionData: selectionData,
            scale: config.mainImageScale,
            offset: config.mainImageOffset
        )

        // 更新缓存
        cachedCircleSelectionImage = renderedImage
        lastCircleSelectionCacheKey = currentImageName

        return renderedImage
    }

    private func getCurrentDisplayImage() -> UIImage? {
        return imageManager.getDisplayImage(for: currentImageName)
    }

    private func getCurrentImageScale() -> CGFloat {
        // 确定正确的上下文
        let modeContext: ModeContext
        if currentImageName.contains("_copy_") {
            modeContext = ModeContext(modeId: currentImageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        let settings = imageManager.getImageSettings(for: currentImageName, in: modeContext)
        if settings.isMultiImageMode {
            let currentImageName = settings.currentDisplayImageName
            return model.getImageScale(for: currentImageName)
        }
        return model.getImageScale(for: currentImageName)
    }

    private func getCurrentImageOffset() -> CGSize {
        // 所有图片（包括圈选裁剪的图片）都应用用户设置的偏移量
        let settings = imageManager.getImageSettings(for: currentImageName)
        if settings.isMultiImageMode {
            let currentImageName = settings.currentDisplayImageName
            return model.getImageOffset(for: currentImageName)
        }
        return model.getImageOffset(for: currentImageName)
    }

    // MARK: - Auto Trigger Methods

    /// 启动自动触发定时器
    private func startAutoTrigger() {
        // 先停止现有定时器
        stopAutoTrigger()

        // 获取自动触发间隔
        let settings = model.imageManager.getImageSettings(for: currentImageName)
        let interval = settings.autoTriggerInterval

        // 立即触发一次
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            handleTrigger(at: nil)
        }

        // 启动定时器进行循环触发
        autoTriggerTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { _ in
            handleTrigger(at: nil)
        }
    }

    /// 停止自动触发定时器
    private func stopAutoTrigger() {
        autoTriggerTimer?.invalidate()
        autoTriggerTimer = nil
    }

    // 预加载圈选裁剪图片到缓存
    private func preloadCircleSelectionImage() {
        // 获取当前图片的自定义触发显示配置
        let config = model.getCustomTriggerDisplay(for: currentImageName)
        
        // 检查是否有主图圈选数据
        guard let selectionData = config.mainCircleSelectionData,
              !selectionData.pathPoints.isEmpty else {
            // 如果没有圈选数据，则清除缓存
            cachedCircleSelectionImage = nil
            lastCircleSelectionCacheKey = ""
            return
        }
        
        // 获取原始图片
        guard let originalImage = model.imageManager.getOriginalImage(for: currentImageName) else {
            // 如果原始图片加载失败，则清除缓存
            cachedCircleSelectionImage = nil
            lastCircleSelectionCacheKey = ""
            return
        }
        
        // 应用圈选裁剪
        let renderedImage = model.applyCircleSelectionToImage(
            originalImage,
            selectionData: selectionData,
            scale: config.mainImageScale,
            offset: config.mainImageOffset
        )
        
        // 更新缓存
        cachedCircleSelectionImage = renderedImage
        lastCircleSelectionCacheKey = currentImageName
    }

    // 清理圈选裁剪图片缓存
    private func invalidateCircleSelectionCache() {
        cachedCircleSelectionImage = nil
        lastCircleSelectionCacheKey = ""
    }
}
