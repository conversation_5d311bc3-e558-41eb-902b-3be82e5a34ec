import SwiftUI
import WatchKit
import PhotosUI

/// ViewMode 表示界面的两种显示模式
enum ViewMode {
    case grid      // 网格视图
    case sounds    // 音效列表
}

// MARK: - 主图片模式界面
struct ImageModeView: View {
    @ObservedObject var model: BugOffModel
    @State private var showingDefaultImagePicker = false
    @State private var showingPhotosPicker = false
    @State private var selectedPhotoItem: PhotosPickerItem?
    @StateObject private var photoService = PhotoSelectionService()
    @State private var isPlaying = false
    @State private var showingFullScreenImage = false
    @State private var showingSettings = false
    @State private var showingSortSheet = false
    @State private var showingDeleteConfirmation = false
    @State private var pendingDeleteImage: String? = nil

    @State private var showAddHint: Bool = false
    @State private var pressedRow: String? = nil
    // 视图模式（两种模式循环切换）
    @State private var viewMode: ViewMode = .grid
    @State private var selectedSoundsForMix: Set<String> = [] // 新增：供首页音效列表选择合成
    @State private var mixerSelectedSounds: [String] = [] // 新增：进入合成视图的已选音效
    @State private var showingMixerSheet: Bool = false // 新增：控制合成视图

    // 视图预加载状态
    @State private var isGridViewLoaded: Bool = false
    @State private var isSoundViewLoaded: Bool = false

    let columns = [
        GridItem(.flexible()),
        GridItem(.flexible())
    ]

    var thumbnailSize: CGFloat {
        return AppTheme.adaptiveSize(80)
    }

    var navigationTitle: String {
        switch viewMode {
        case .grid:
            return "图片网格"
        case .sounds:
            return "音效列表"
        }
    }

    var viewModeIcon: String {
        switch viewMode {
        case .grid:
            return "speaker.wave.2.fill"  // 当前是网格，点击切换到音效
        case .sounds:
            return "square.grid.2x2"  // 当前是音效，点击切换到网格
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // 使用 ZStack + opacity 方案来保持视觉连续性
                // 避免视图切换时模糊效果突然消失的问题
                gridView
                    .opacity(viewMode == .grid ? 1 : 0)
                    .zIndex(viewMode == .grid ? 1 : 0)
                    .animation(.easeInOut(duration: 0.2), value: viewMode)

                soundView
                    .opacity(viewMode == .sounds ? 1 : 0)
                    .zIndex(viewMode == .sounds ? 1 : 0)
                    .animation(.easeInOut(duration: 0.2), value: viewMode)
            }
//            .navigationTitle(navigationTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button(action: {
                        // 在显示设置视图之前停止所有音效
                        model.stopSound()
                        showingSettings = true
                    }) {
                        Image(systemName: "gearshape.fill")
                            .font(.system(size: AppTheme.smallIconSize))
                            .foregroundColor(AppTheme.primaryColor)
                    }
                }
                
                ToolbarItem(placement: .topBarTrailing) {
                    HStack(spacing: 12) {
                        // 合成按钮（始终占位，使用透明度与缩放过渡）
                        Button(action: {
                            // 根据选中顺序生成合成视图列表
                            mixerSelectedSounds = model.selectedSoundsOrder.filter { selectedSoundsForMix.contains($0) }
                            showingMixerSheet = true
                        }) {
                            Image(systemName: "waveform.path.badge.plus")
                                .font(.system(size: AppTheme.smallIconSize))
                                .foregroundColor(AppTheme.primaryColor)
                        }
                        // 动态显示/隐藏
                        .opacity(viewMode == .sounds && !selectedSoundsForMix.isEmpty ? 1 : 0)
                        .scaleEffect(viewMode == .sounds && !selectedSoundsForMix.isEmpty ? 1 : 0.6)
                        .animation(.easeInOut(duration: 0.25), value: viewMode == .sounds && !selectedSoundsForMix.isEmpty)
                        .disabled(!(viewMode == .sounds && !selectedSoundsForMix.isEmpty))
                        Button(action: {
                            // 使用更快速的动画提升响应性
                            withAnimation(.easeInOut(duration: 0.2)) {
                                switch viewMode {
                                case .grid:
                                    viewMode = .sounds
                                    preloadSoundView()
                                case .sounds:
                                    viewMode = .grid
                                    preloadGridView()
                                }
                            }
                        }) {
                            Image(systemName: viewModeIcon)
                                .font(.system(size: AppTheme.smallIconSize))
                                .foregroundColor(AppTheme.primaryColor)
                        }
                    }
                }
            }
            .sheet(isPresented: $showingDefaultImagePicker) {
                DefaultImagePicker(model: model)
            }
            .photosPicker(
                isPresented: $showingPhotosPicker,
                selection: $selectedPhotoItem,
                matching: .images
            )
            .onChange(of: selectedPhotoItem) { _, newItem in
                Task {
                    await handleSelectedPhotoWithService(newItem)
                }
            }
            .sheet(isPresented: $showingSettings) {
                // TODO: 这里后续需要实现设置页面
                Text("设置页面")
                    .navigationTitle("设置")
            }
            .sheet(isPresented: $showingMixerSheet) {
                NavigationStack {
                    SoundMixerView(
                        model: model,
                        selectedSounds: $mixerSelectedSounds,
                        isPresented: $showingMixerSheet,
                           imageName: "" // 首页进入的合成视图不关联特定图片
                    )
                }
                .environmentObject(model.soundManager)
            }
            .fullScreenCover(isPresented: $showingFullScreenImage) {
                FullScreenImageView(
                    defaultImageName: model.selectedDefaultImageName,
                    isPresented: $showingFullScreenImage,
                    model: model
                )
                .environmentObject(model.imageManager)
                .environmentObject(model.soundManager)
                .environmentObject(model.triggerManager)
            }
            // 删除确认对话框
            .alert("确认删除该mode?", isPresented: Binding(
                get: { pendingDeleteImage != nil },
                set: { if !$0 { pendingDeleteImage = nil } }
            )) {
                Button("取消", role: .cancel) {}
                Button("删除", role: .destructive) {
                    if let img = pendingDeleteImage { deleteImage(img) }
                    pendingDeleteImage = nil
                }
            }
            .onAppear {
                // 立即预加载所有视图，确保切换时的流畅性
                preloadAllViews()

                // 提前预热音效数据，避免首次切换时的延迟
                Task.detached(priority: .background) {
                    if AppConfig.enableSoundDataCache {
                        await model.soundManager.audioService.prewarmAsync(sounds: model.defaultSounds)
                    }
                }

                // 延迟显示添加提示，避免与其他动画冲突
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    if model.defaultImages.isEmpty {
                        showAddHint = true
                    }
                }
            }
            .onDisappear {
                // 当离开首页时，停止所有正在播放的音效
                model.stopSound()
            }
        }
    }

    // MARK: - 图片操作
    private func deleteImage(_ imageName: String) {
        guard let index = model.defaultImages.firstIndex(of: imageName) else { return }
        // 删除关联
        model.imageManager.deleteImage(imageName)
        model.soundManager.removeSoundsForImage(imageName)
        model.triggerManager.removeTriggerSettings(for: imageName)
        model.defaultImages.remove(at: index)
        // 如果删除当前选中图片，切换到第一张
        if model.selectedDefaultImageName == imageName {
            model.selectedDefaultImageName = model.defaultImages.first ?? ""
        }
        model.saveImageOrder()
        WKInterfaceDevice.current().play(.success)

        // 如果当前处于网格模式且已无图片，显示一次性提示
        if viewMode == .grid && model.defaultImages.isEmpty {
            showAddHint = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                withAnimation { showAddHint = false }
            }
        }
    }

    private func moveImages(from offsets: IndexSet, to destination: Int) {
        model.defaultImages.move(fromOffsets: offsets, toOffset: destination)
        model.saveImageOrder()
    }

    /// 使用PhotoSelectionService处理从PhotosPicker选中的图片
    @MainActor
    private func handleSelectedPhotoWithService(_ item: PhotosPickerItem?) async {
        guard let newImageName = await photoService.handleModeImageSelection(item, model: model) else {
            // 播放失败反馈
            WKInterfaceDevice.current().play(.failure)
            selectedPhotoItem = nil
            return
        }

        // 添加到图片列表
        model.defaultImages.insert(newImageName, at: 0)
        model.saveImageOrder()

        // 设置为当前选中图片
        model.selectedDefaultImageName = newImageName

        // 播放成功反馈
        WKInterfaceDevice.current().play(.success)

        // 预加载缩略图
        Task {
            _ = await ThumbnailGenerator.thumbnail(for: newImageName, size: thumbnailSize, model: model)
        }

        // 清除选中状态
        selectedPhotoItem = nil
    }

    // MARK: - 预加载视图组件

    @ViewBuilder
    private var gridView: some View {
        if isGridViewLoaded {
            ImageGridManageView(
                model: model,
                showingFullScreen: $showingFullScreenImage,
                thumbnailSize: thumbnailSize,
                showAddHint: $showAddHint,
                onAddTap: {
                    // PhotosPicker会自动处理权限请求
                    showingPhotosPicker = true
                },
                onOpenSettings: { showingSettings = true }
            )
            .environmentObject(model.imageManager)
            .environmentObject(model.soundManager)
        } else {
            Color.clear
        }
    }

    @ViewBuilder
    private var soundView: some View {
        if isSoundViewLoaded {
            SoundListView(
                model: model,
                mode: .edit,  // 首页进入使用编辑模式
                selectedSound: .constant(nil),
                selectedSounds: $selectedSoundsForMix, // 绑定到本地状态
                onSoundSelected: nil,
                onSoundsUpdated: nil,
                imageName: nil  // 首页音效列表不关联特定图片，使用全局配置
            )
        } else {
            Color.clear
        }
    }

    // MARK: - 预加载方法

    private func preloadCurrentView() {
        switch viewMode {
        case .grid:
            preloadGridView()
        case .sounds:
            preloadSoundView()
        }
    }

    private func preloadAllViews() {
        preloadGridView()
        preloadSoundView()
    }

    private func preloadGridView() {
        if !isGridViewLoaded {
            isGridViewLoaded = true

            // 预加载缩略图，减少闪烁
            ThumbnailGenerator.preloadThumbnails(
                for: model.defaultImages,
                size: thumbnailSize,
                model: model
            )
        }
    }



    private func preloadSoundView() {
        if !isSoundViewLoaded {
            isSoundViewLoaded = true

            // 异步预热音效数据，避免阻塞UI
            Task.detached(priority: .userInitiated) {
                if AppConfig.enableSoundDataCache {
                    await model.soundManager.audioService.prewarmAsync(sounds: model.defaultSounds)
                }
            }
        }
    }
}

struct DefaultImagePicker: View {
    @ObservedObject var model: BugOffModel
    @Environment(\.presentationMode) var presentationMode

    let columns = [
        GridItem(.flexible()),
        GridItem(.flexible())
    ]

    var thumbnailSize: CGFloat {
        return AppTheme.adaptiveSize(70)
    }

    var body: some View {
        VStack {
            Text("选择图片")
                .titleTextStyle()
                .padding(.top, 8)

            ScrollView {
                LazyVGrid(columns: columns, spacing: 8) {
                    ForEach(model.defaultImages, id: \.self) { imageName in
                        Image(imageName)
                            .resizable()
                            .scaledToFill()
                            .frame(width: thumbnailSize, height: thumbnailSize)
                            .clipShape(RoundedRectangle(cornerRadius: AppTheme.cornerRadius))
                            .overlay(
                                RoundedRectangle(cornerRadius: AppTheme.cornerRadius)
                                    .stroke(model.selectedDefaultImageName == imageName ? Color.blue : Color.clear, lineWidth: 2)
                            )
                            .onTapGesture {
                                model.selectedDefaultImageName = imageName
                                presentationMode.wrappedValue.dismiss()
                            }
                    }
                }
                .padding(Sizes.smallPadding)
            }

            Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            }
            .primaryCapsuleStyle()
            .padding(.horizontal, Sizes.mediumPadding)
            .padding(.bottom, Sizes.smallPadding)
        }
    }
}

struct ImageModeView_Previews: PreviewProvider {
    static var previews: some View {
        let model = BugOffModel()

        // 注入关键的环境对象，让预览环境更稳定
        ImageModeView(model: model)
            .environmentObject(model.imageManager)
            .environmentObject(model.soundManager)
            .environmentObject(model.triggerManager)
    }
}
