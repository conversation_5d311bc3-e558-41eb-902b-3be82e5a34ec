import SwiftUI
import WatchKit
import Foundation
import AVFoundation

/// 图片设置视图 - 提供图片裁剪、音效选择和播放模式配置功能
struct ImageSettingsView: View {
    // MARK: - Properties
    @ObservedObject var model: BugOffModel
    let imageName: String
    @Binding var isPresented: Bool
    @State private var showingSoundSelector = false
    @State private var pushSoundSelector = false
    @State private var selectedSounds: Set<String> = Set<String>()
    @State private var showingImageEditor = false
    @State private var showingImageSizeEditor = false
    @State private var showingImageProportionEditor = false
    @State private var triggerMode: ImageTriggerMode
    @State private var currentImageName: String
    @State private var showingCustomTriggerConfig = false
    @State private var showingCustomImageTriggerConfig = false
    @State private var showResetConfirmation = false

    // 当前使用的mode上下文
    private let currentModeContext: ModeContext
    @State private var enableBacktrack: Bool // 添加回溯功能开关状态
    @State private var backtrackDuration: TimeInterval? // 音效回溯时长
    @State private var showingBacktrackControl: Bool = false // 回溯控制展开状态

    // 新增：mode管理相关状态
    @State private var showingDeleteConfirmation: Bool = false
    @State private var showingCopyConfirmation: Bool = false


    
    // 添加缺失的状态变量
    @State private var isCropping: Bool = false
    // 暂存的裁剪结果
    @State private var tempCroppedImage: UIImage?
    @State private var tempScale: CGFloat = 1.0
    @State private var tempOffset: CGSize = .zero
    @State private var tempFileURL: URL?
    @State private var hasPendingChanges = false
    
    // 动画状态
    @State private var isAnimating = false
    
    // 动态颜色预览状态
    @State private var previewColors: [Color] = []
    @State private var currentColorIndex: Int = 0
    @State private var colorChangeTimer: Timer?
    
    // 累计次数状态 - 用于强制界面更新
    @State private var currentTriggerCount: Int = 0
    // 自动触发时间间隔状态
    @State private var autoTriggerInterval: Double = 2.0
    


    @State private var soundPlayMode: SoundPlayMode
    
    // 强制UI刷新的状态变量
    @State private var configUpdateTrigger: Bool = false

    // 自定义显示区域展开状态
    @State private var customDisplayExpanded: Bool = false

    // 性能优化：缓存配置以减少频繁获取
    @State private var cachedCustomTriggerDisplay: CustomTriggerDisplay?

    // 触发提示开关状态
    @State private var triggerHintEnabled: Bool = true

    // 随机提示开关状态
    @State private var randomHintEnabled: Bool = false

    // 新增：图片模式显示名称

    
    // MARK: - Initialization
    init(model: BugOffModel, imageName: String, isPresented: Binding<Bool>) {
        self.model = model
        self.imageName = imageName
        self._isPresented = isPresented
        self._currentImageName = State(initialValue: imageName)

        // 确定正确的mode上下文
        if imageName.contains("_copy_") {
            // 复制的mode，使用其自己的上下文
            self.currentModeContext = ModeContext(modeId: imageName)
            print("🔧 检测到复制mode，使用专用上下文: \(self.currentModeContext)")
        } else {
            // 原始mode，使用当前上下文
            self.currentModeContext = model.imageManager.getCurrentModeContext()
            print("🔧 使用当前上下文: \(self.currentModeContext)")
        }

        // 使用正确的上下文加载配置
        let settings = model.imageManager.getImageSettings(for: imageName, in: self.currentModeContext)
        print("🔧 加载配置 - enableBacktrack: \(settings.enableBacktrack), randomHintEnabled: \(settings.randomHintEnabled)")

        self._triggerMode = State(initialValue: model.triggerManager.getTriggerMode(for: imageName, imageManager: model.imageManager))

        // 加载已选择的音效
        let sounds = Set(model.imageMultiSounds[imageName] ?? [])
        self._selectedSounds = State(initialValue: sounds)

        // 使用正确上下文的配置初始化状态
        self._soundPlayMode = State(initialValue: settings.soundPlayMode)
        self._enableBacktrack = State(initialValue: settings.enableBacktrack)
        self._backtrackDuration = State(initialValue: settings.backtrackDuration)
        self._autoTriggerInterval = State(initialValue: settings.autoTriggerInterval)
        self._randomHintEnabled = State(initialValue: settings.randomHintEnabled)

        // 初始化累计次数
        self._currentTriggerCount = State(initialValue: model.triggerManager.getCurrentTriggerCount(for: imageName, imageManager: model.imageManager))

        // 初始化触发提示开关状态（默认开启）
        let customDisplay = model.triggerManager.getCustomTriggerDisplay(for: imageName)
        self._triggerHintEnabled = State(initialValue: customDisplay.isEnabled)

    }

    // MARK: - Computed Properties

    /// 判断当前mode image是否为圈选图片
    private var isCircleSelectionImage: Bool {
        let config = model.getCustomTriggerDisplay(for: currentImageName)
        return config.mainCircleSelectionData != nil &&
               !(config.mainCircleSelectionData?.pathPoints.isEmpty ?? true)
    }

    // MARK: - Body
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: AppTheme.mediumPadding) {
                    cropSection

                    // 图片占比功能行（仅当mode image为圈选图片时显示）
                    if isCircleSelectionImage {
                        imageProportionSection
                    }
                    
                    // 音效导航入口
                    Button(action: {
                        // 保证顺序
                        let existingOrder = model.selectedSoundsOrder
                        for sound in selectedSounds {
                            if !existingOrder.contains(sound) {
                                model.selectedSoundsOrder.append(sound)
                            }
                        }
                        pushSoundSelector = true
                    }) {
                        soundSection
                    }
                    .buttonStyle(PlainButtonStyle())

                    // 播放模式功能行（在音效选择下方，选择大于1个音效时显示）
                    if selectedSounds.count > 1 {
                        playModeSection
                    }

                    // 回溯功能开关（选择1个音效时显示）
                    if selectedSounds.count == 1 {
                        backtrackSection

                        // 音效回溯功能行（当回溯开关开启时显示）
                        if enableBacktrack {
                            backtrackDurationSection
                        }
                    }

                    // 已移除"停止上次播放"功能行

                    // 触发提示开关功能行
                    triggerHintSection

                    // 随机提示功能行（当触发提示开关开启时显示）
                    if triggerHintEnabled {
                        randomHintSection
                    }

                    // 自定义显示功能行（当触发提示开关开启且随机提示关闭时显示）
                    if triggerHintEnabled && !randomHintEnabled {
                        customDisplaySection
                    }
                    
                    // 触发方式功能行常驻，不依赖音效选择
                    triggerModeSection
                    
                    // 累计次数行常驻到触发方式下方
                    clickCountDetailsSection

                    // Mode管理功能区域
                    copyModeSection

                    resetButtonSection

                    // 删除功能（最危险操作放在最底部）
                    deleteModeSection
                }
            }
            .navigationTitle("图片设置")
            .navigationBarTitleDisplayMode(.inline)

            .onAppear {
                // 重新加载设置以确保状态同步，使用正确的上下文
                let settings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
                autoTriggerInterval = settings.autoTriggerInterval
                enableBacktrack = settings.enableBacktrack
                soundPlayMode = settings.soundPlayMode
                randomHintEnabled = settings.randomHintEnabled

                // 异步加载配置，避免主线程hang
                if cachedCustomTriggerDisplay == nil {
                    // 先使用默认配置，避免UI阻塞
                    cachedCustomTriggerDisplay = CustomTriggerDisplay()
                    triggerHintEnabled = true

                    // 在后台异步加载真实配置
                    DispatchQueue.global(qos: .userInitiated).async {
                        let config = model.triggerManager.getCustomTriggerDisplay(for: currentImageName)
                        DispatchQueue.main.async {
                            cachedCustomTriggerDisplay = config
                            triggerHintEnabled = config.isEnabled
                            // 配置加载完成后启动颜色动画
                            startColorAnimation()
                        }
                    }
                } else {
                    // 如果已有缓存，直接使用
                    triggerHintEnabled = cachedCustomTriggerDisplay?.isEnabled ?? true
                    startColorAnimation()
                }

                // 每次回到页面时，从模型中重新加载音效选择状态
                let sounds = Set(model.imageMultiSounds[currentImageName] ?? [])
                selectedSounds = sounds

                // 更新累计次数
                currentTriggerCount = model.triggerManager.getCurrentTriggerCount(for: currentImageName, imageManager: model.imageManager)
            }
            .onChange(of: selectedSounds) { oldSounds, newSounds in
                print("🎵 音效选择变化 - 旧: \(oldSounds), 新: \(newSounds)")

                // 当音效选择发生变化时，处理回溯相关状态
                if newSounds.count == 1 {
                    // 选择了单个音效，检查回溯时长是否需要调整
                    let newSoundName = newSounds.first!
                    let newSoundDuration = model.soundManager.getSoundDuration(for: newSoundName)
                    print("🎵 新音效时长: \(newSoundName) = \(newSoundDuration)s")

                    // 如果当前回溯时长超过了新音效的总时长，需要调整
                    if let currentBacktrackDuration = backtrackDuration, currentBacktrackDuration > newSoundDuration {
                        print("🎵 回溯时长调整: \(currentBacktrackDuration)s -> \(newSoundDuration)s (音效时长限制)")
                        backtrackDuration = newSoundDuration
                        saveBacktrackDuration()
                    }

                    // 保持回溯开关状态不变（如果之前开启了回溯，继续保持开启）
                } else {
                    // 选择了多个音效或没有音效，关闭回溯功能
                    showingBacktrackControl = false
                    enableBacktrack = false
                    backtrackDuration = nil

                    // 保存回溯开关状态到ImageSettings，使用正确的上下文
                    var settings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
                    settings.enableBacktrack = false
                    settings.backtrackDuration = nil
                    model.imageManager.updateImageSettings(for: currentImageName, in: currentModeContext, settings: settings)
                    print("🎵 多音效模式，已关闭回溯功能")
                }
            }
            .onDisappear {
                // 视图关闭时自动保存设置
                saveSettingsOnClose()
            }
            .navigationDestination(isPresented: $pushSoundSelector) {
                SoundListView(
                    model: model,
                    mode: .modeSettings,  // 从mode进入使用设置模式
                    selectedSound: .constant(nil),
                    selectedSounds: $selectedSounds,
                    onSoundSelected: nil,
                    onSoundsUpdated: {
                        updateImageSounds()
                    },
                    imageName: currentImageName
                )
            }


        }
        .sheet(isPresented: $showingImageEditor) {
            ImageSizeEditorView.createDirectSaveEditor(
                model: model,
                imageName: currentImageName
            )
        }
        // ModeEditView已移除，直接在设置视图中编辑
        .alert("确认复制该Mode?", isPresented: $showingCopyConfirmation) {
            Button("取消", role: .cancel) { }
            Button("复制") {
                copyModeWithIsolation()
            }
        } message: {
            Text("将创建一个包含所有配置的Mode副本")
        }
        .alert("确认删除该Mode?", isPresented: $showingDeleteConfirmation) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                deleteModeAndClose()
            }
        } message: {
            Text("此操作不可撤销，将删除Mode及其所有配置")
        }
        .sheet(isPresented: $showingImageSizeEditor, onDismiss: {
            // 从图片裁剪返回时刷新音效选择状态
            let sounds = Set(model.imageMultiSounds[currentImageName] ?? [])
            selectedSounds = sounds
            
            // 刷新其他状态
            cachedCustomTriggerDisplay = model.triggerManager.getCustomTriggerDisplay(for: currentImageName)
            currentTriggerCount = model.triggerManager.getCurrentTriggerCount(for: currentImageName, imageManager: model.imageManager)
        }) {
            // 自定义编辑器：同时支持传统裁剪和圈选裁剪
            ImageSizeEditorView(
                model: model,
                imageName: currentImageName,
                saveMode: .callback, // 使用回调模式
                onCropCompleted: { image, scale, offset, url in
                    // 传统裁剪回调 - 保存裁剪后的图片作为mode图片
                    self.model.imageManager.updateCroppedImage(for: self.currentImageName, croppedImageURL: url)
                    self.model.imageScales[self.currentImageName] = scale
                    self.model.imageOffsets[self.currentImageName] = offset

                    // 清理Toast图片缓存，确保下次获取时使用最新的裁剪结果
                    self.model.triggerManager.refreshToastImageCache(for: self.currentImageName)
                    
                    // 使缩略图缓存失效，确保首页缩略图也能显示裁剪效果
                    ThumbnailGenerator.invalidateAll()
                    
                    // 发送通知，通知全屏视图更新圈选裁剪缓存
                    NotificationCenter.default.post(
                        name: NSNotification.Name("CircleSelectionUpdated"),
                        object: nil,
                        userInfo: ["imageName": self.currentImageName]
                    )
                    
                    // 关闭编辑器
                    self.showingImageSizeEditor = false
                },
                onConfigCompleted: nil,
                onCircleSelectionCompleted: { pathPoints, cropRect, scale, offset in
                    // 圈选裁剪回调 - 保存圈选数据

                    // 圈选裁剪完成后，重置偏移量为零，确保圈选结果居中显示
                    self.model.imageScales[self.currentImageName] = 1.0  // 重置缩放为1倍
                    self.model.imageOffsets[self.currentImageName] = .zero  // 重置偏移为零
                    
                    // 保存圈选数据到ImageSettings中（用于全屏图片显示）
                    var settings = self.model.imageManager.getImageSettings(for: self.currentImageName)
                    settings.scale = 1.0  // 重置缩放为1倍
                    settings.offset = .zero  // 重置偏移为零
                    // 注意：ImageSettings中还没有圈选路径字段，这里主要保存缩放和偏移
                    self.model.imageManager.updateImageSettings(for: self.currentImageName, settings: settings)
                    
                    // 将圈选数据保存到CustomTriggerDisplay中，但区分主图和Toast图片
                    var customConfig = self.model.triggerManager.getCustomTriggerDisplay(for: self.currentImageName)

                    // 确保displayMode为image，这样才能进入正确的保存分支
                    customConfig.displayMode = .image

                    // 保存到主图圈选数据字段
                    customConfig.mainCircleSelectionData = CircleSelectionData(
                        pathPoints: pathPoints,
                        boundingRect: cropRect
                    )
                    // 保存圈选时的缩放和偏移，用于圈选裁剪算法
                    customConfig.mainImageScale = scale
                    customConfig.mainImageOffset = offset
                    
                    // 保持向后兼容的字段
                    customConfig.customCropRect = cropRect
                    customConfig.customCropPath = pathPoints
                    
                    self.model.triggerManager.setCustomTriggerDisplay(for: self.currentImageName, config: customConfig)

                    print("📱 Mode设置中的圈选结果已保存:")
                    print("   路径点数量: \(pathPoints.count)")
                    print("   裁剪区域: \(cropRect)")
                    print("   缩放: \(scale)")
                    print("   偏移: \(offset)")

                    // 验证保存结果
                    let savedConfig = self.model.triggerManager.getCustomTriggerDisplay(for: self.currentImageName)
                    print("📱 验证保存结果:")
                    print("   mainCircleSelectionData: \(savedConfig.mainCircleSelectionData != nil ? "存在(\(savedConfig.mainCircleSelectionData!.pathPoints.count)点)" : "nil")")
                    print("   mainImageScale: \(savedConfig.mainImageScale)")
                    print("   mainImageOffset: \(savedConfig.mainImageOffset)")
                    
                    // 清理Toast图片缓存，确保下次获取时使用最新的裁剪结果
                    self.model.triggerManager.refreshToastImageCache(for: self.currentImageName)
                    
                    // 使缩略图缓存失效，确保首页缩略图也能显示圈选裁剪效果
                    ThumbnailGenerator.invalidateAll()
                    
                    // 发送通知，通知全屏视图更新圈选裁剪缓存
                    NotificationCenter.default.post(
                        name: NSNotification.Name("CircleSelectionUpdated"),
                        object: nil,
                        userInfo: ["imageName": self.currentImageName]
                    )

                    // 关闭编辑器
                    self.showingImageSizeEditor = false
                },
                useCustomImage: false // mode设置不使用自定义图片
            )
        }
        .sheet(isPresented: $showingCustomTriggerConfig, onDismiss: {
            // 从文字自定义设置返回时异步刷新UI和缓存
            DispatchQueue.global(qos: .userInitiated).async {
                let config = model.triggerManager.getCustomTriggerDisplay(for: currentImageName)
                DispatchQueue.main.async {
                    cachedCustomTriggerDisplay = config
                    configUpdateTrigger.toggle()
                    startColorAnimation()
                }
            }
        }) {
            CustomTriggerConfigView(
                model: model,
                imageName: currentImageName,
                isPresented: $showingCustomTriggerConfig
            )
        }
        .sheet(isPresented: $showingCustomImageTriggerConfig, onDismiss: {
            // 从图片自定义设置返回时异步刷新UI和缓存
            DispatchQueue.global(qos: .userInitiated).async {
                let config = model.triggerManager.getCustomTriggerDisplay(for: currentImageName)
                DispatchQueue.main.async {
                    cachedCustomTriggerDisplay = config
                    configUpdateTrigger.toggle()
                    startColorAnimation()
                }
            }
        }) {
            CustomImageTriggerConfigView(
                model: model,
                imageName: currentImageName,
                isPresented: $showingCustomImageTriggerConfig
            )
        }
        .sheet(isPresented: $showingImageProportionEditor) {
            ImageProportionEditorView(
                model: model,
                imageName: currentImageName,
                onSave: { scale, offset in
                    // 保存图片占比设置
                    var settings = model.imageManager.getImageSettings(for: currentImageName)
                    settings.scale = scale
                    model.imageManager.updateImageSettings(for: currentImageName, settings: settings)
                    
                    // 更新模型中的缩放和偏移
                    model.imageScales[currentImageName] = scale
                    model.imageOffsets[currentImageName] = offset
                    
                    // 发送通知，通知全屏视图更新显示
                    NotificationCenter.default.post(
                        name: NSNotification.Name("ImageSizeUpdated"),
                        object: nil,
                        userInfo: ["imageName": currentImageName, "scale": scale]
                    )
                    
                    print("📱 图片占比设置已保存: scale=\(scale), offset=\(offset)")
                }
            )
        }
        .onDisappear(perform: stopColorAnimation)
    }
    
    // MARK: - View Sections
    

    
    private var cropSection: some View {
        Button(action: { showingImageSizeEditor = true }) {
            HStack(alignment: .center) {
                HStack(spacing: AppTheme.smallPadding) {
                    Image(systemName: "crop")
                        .foregroundColor(AppTheme.primaryColor)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                    Text("图片裁剪")
                        .font(.appBody)
                        .foregroundColor(Color.textPrimary)
                }
                Spacer()
                Image(systemName: "chevron.right")
                    .font(.appSmall)
                    .foregroundColor(Color.gray)
            }
            .standardRowStyle()
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var imageProportionSection: some View {
        Button(action: { showingImageProportionEditor = true }) {
            HStack(alignment: .center) {
                HStack(spacing: AppTheme.smallPadding) {
                    Image(systemName: "aspectratio")
                        .foregroundColor(AppTheme.primaryColor)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                    Text("图片占比")
                        .font(.appBody)
                        .foregroundColor(Color.textPrimary)
                }
                Spacer()
                Image(systemName: "chevron.right")
                    .font(.appSmall)
                    .foregroundColor(Color.gray)
            }
            .standardRowStyle()
        }
        .buttonStyle(PlainButtonStyle())
    }
    

    
    private var soundSection: some View {
        // 音效标题行 - 整行可点击
        HStack(alignment: .center) {
            // 左侧图标和文本组
            HStack(spacing: AppTheme.smallPadding) {
                Image(systemName: "speaker.wave.2")
                    .foregroundColor(AppTheme.primaryColor)
                    .font(.system(size: AppTheme.smallIconSize))
                    .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                
                Text("触发音效")
                    .font(.appBody)
                    .foregroundColor(Color.textPrimary)
            }
            
            Spacer()
            
            // 右侧状态和箭头
            HStack(spacing: 4) {
                // 显示已选音效数量或提示添加
                if selectedSounds.isEmpty {
                    Text("选择")
                        .font(.appSmall)
                        .foregroundColor(Color.gray)
                } else {
                    Text("\(selectedSounds.count) 个")
                        .font(.appSmall)
                        .foregroundColor(Color.gray)
                }

                Image(systemName: "chevron.right")
                    .font(.appSmall)
                    .foregroundColor(Color.gray)
            }
        }
        .standardRowStyle()
    }
    

    
    private var triggerModeSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.smallPadding) {
            // 触发方式设置
            Button(action: {
                withAnimation(Animation.springAnimation()) {
                    // 循环切换触发模式
                    switch triggerMode {
                    case .tap:
                        triggerMode = .shake
                    case .shake:
                        triggerMode = .auto
                    case .auto:
                        triggerMode = .tap
                    }

                    // 立即更新模型
                    model.triggerManager.setTriggerMode(for: currentImageName, mode: triggerMode, imageManager: model.imageManager)

                    // 发送触发模式变化通知
                    NotificationCenter.default.post(
                        name: NSNotification.Name("TriggerModeChanged"),
                        object: nil,
                        userInfo: ["imageName": currentImageName, "triggerMode": triggerMode.rawValue]
                    )

                    // 添加动画效果
                    isAnimating = true
                    DispatchQueue.main.asyncAfter(deadline: .now() + AppTheme.animationDuration) {
                        isAnimating = false
                    }
                }
            }) {
                HStack(alignment: .center) {
                    // 左侧图标和文本组
                    HStack(spacing: AppTheme.smallPadding) {
                        Image(systemName: getTriggerModeIcon())
                            .foregroundColor(AppTheme.primaryColor)
                            .font(.system(size: AppTheme.smallIconSize))
                            .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)

                        Text("触发方式")
                            .font(.appBody)
                            .foregroundColor(Color.textPrimary)
                    }

                    Spacer()

                    // 右侧状态
                    Text(getTriggerModeText())
                        .font(.appSmall)
                        .foregroundColor(Color.gray)
                }
                .standardRowStyle()
            }
            .buttonStyle(PlainButtonStyle())

            // 自动触发时间间隔滑块（仅在自动模式时显示）
            if triggerMode == .auto {
                autoTriggerIntervalSection
            }
        }
    }
    

    
    private var clickCountDetailsSection: some View {
        HStack(alignment: .center) {
            Text("累计次数:")
                .font(.appSmall)
                .foregroundColor(Color.gray)

            AutoScrollingView {
                Text("\(currentTriggerCount)")
                    .font(.appSmall)
                    .foregroundColor(Color.gray)
            }
            
            Spacer()
            
            Button(action: {
                // 显示确认弹窗
                showResetConfirmation = true
            }) {
                Text("重置")
                    .font(.appSmall)
                    .foregroundColor(AppTheme.secondaryColor)
                    .fixedSize()
            }
            .buttonStyle(PlainButtonStyle())
            .confirmationDialog(
                "确认重置",
                isPresented: $showResetConfirmation,
                titleVisibility: .visible
            ) {
                Button("重置", role: .destructive) {
                    // 重置模型中的触发次数
                    model.triggerManager.resetTriggerCount(for: currentImageName, imageManager: model.imageManager)
                    // 立即更新界面状态
                    currentTriggerCount = 0
                }
                Button("取消", role: .cancel) {
                    // 取消操作，什么都不做
                }
            } message: {
                Text("累计次数将重置为0，此操作无法撤销。")
            }
        }
        .padding(.horizontal, AppTheme.largePadding)
        .padding(.top, -Sizes.tinyPadding)
    }

    /// 触发提示开关功能行
    private var triggerHintSection: some View {
        Button(action: {
            // 切换开关状态
            triggerHintEnabled.toggle()
        }) {
            HStack(alignment: .center) {
                // 左侧图标和文本组
                HStack(spacing: AppTheme.smallPadding) {
                    Image(systemName: "bubble.fill")
                        .foregroundColor(AppTheme.primaryColor)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)

                    Text("触发提示")
                        .font(.appBody)
                        .foregroundColor(Color.textPrimary)
                }

                Spacer()

                // 右侧开关
                Toggle("", isOn: $triggerHintEnabled)
                    .labelsHidden()
                    .allowsHitTesting(false) // 禁用Toggle的点击，让Button处理
            }
            .standardRowStyle()
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .onChange(of: triggerHintEnabled) { _, newValue in
            // 更新自定义显示配置的启用状态
            var config = cachedCustomTriggerDisplay ?? model.triggerManager.getCustomTriggerDisplay(for: currentImageName)
            config.isEnabled = newValue
            model.triggerManager.setCustomTriggerDisplay(for: currentImageName, config: config)
            // 更新缓存
            cachedCustomTriggerDisplay = config

            // 如果关闭了触发提示，同时关闭自定义显示展开状态
            if !newValue {
                customDisplayExpanded = false
            }
        }
    }

    /// 随机提示开关功能行
    private var randomHintSection: some View {
        Button(action: {
            // 切换随机提示状态
            randomHintEnabled.toggle()
        }) {
            HStack(alignment: .center) {
                // 左侧图标和文本组
                HStack(spacing: AppTheme.smallPadding) {
                    Image(systemName: "dice.fill")
                        .foregroundColor(AppTheme.primaryColor)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)

                    Text("随机提示")
                        .font(.appBody)
                        .foregroundColor(Color.textPrimary)
                }

                Spacer()

                // 右侧开关
                Toggle("", isOn: $randomHintEnabled)
                    .labelsHidden()
                    .allowsHitTesting(false) // 禁用Toggle的点击，让Button处理
            }
            .standardRowStyle()
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .onChange(of: randomHintEnabled) { _, newValue in
            // 保存随机提示状态到模型，使用正确的上下文
            var settings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
            settings.randomHintEnabled = newValue
            model.imageManager.updateImageSettings(for: currentImageName, in: currentModeContext, settings: settings)

            // 如果开启随机提示，关闭自定义显示的展开状态
            if newValue {
                customDisplayExpanded = false
            }
        }
    }

    private var customDisplaySection: some View {
        // 性能优化：确保配置已缓存，避免首次渲染时的延迟
        let config = cachedCustomTriggerDisplay ?? CustomTriggerDisplay()

        return CustomDisplayRow(
            config: config,
            previewColors: previewColors,
            currentColorIndex: currentColorIndex,
            onTextCustomization: {
                // 立即更新UI状态，延迟保存数据
                customDisplayExpanded = true
                showingCustomTriggerConfig = true

                // 在后台线程延迟执行数据保存，避免hang
                DispatchQueue.global(qos: .userInitiated).async {
                    var updatedConfig = cachedCustomTriggerDisplay ?? model.triggerManager.getCustomTriggerDisplay(for: currentImageName)
                    updatedConfig.displayMode = .text
                    model.triggerManager.setCustomTriggerDisplay(for: currentImageName, config: updatedConfig)

                    // 在主线程更新缓存
                    DispatchQueue.main.async {
                        cachedCustomTriggerDisplay = updatedConfig
                    }
                }
            },
            onImageCustomization: {
                // 立即更新UI状态，延迟保存数据
                customDisplayExpanded = true
                showingCustomImageTriggerConfig = true

                // 在后台线程延迟执行数据保存，避免hang
                DispatchQueue.global(qos: .userInitiated).async {
                    var updatedConfig = cachedCustomTriggerDisplay ?? model.triggerManager.getCustomTriggerDisplay(for: currentImageName)
                    updatedConfig.displayMode = .image
                    model.triggerManager.setCustomTriggerDisplay(for: currentImageName, config: updatedConfig)

                    // 在主线程更新缓存
                    DispatchQueue.main.async {
                        cachedCustomTriggerDisplay = updatedConfig
                    }
                }
            },
            isExpanded: $customDisplayExpanded
        )
    }
    

    
    // 已移除"停止上次播放"功能，因为与回溯功能冲突

    /// 另存为Mode功能区域
    private var copyModeSection: some View {
        Button(action: { showingCopyConfirmation = true }) {
            HStack(alignment: .center) {
                HStack(spacing: AppTheme.smallPadding) {
                    Image(systemName: "doc.on.doc")
                        .foregroundColor(AppTheme.successColor)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                    Text("另存为")
                        .font(.appBody)
                        .foregroundColor(Color.textPrimary)
                }
                Spacer()
            }
            .standardRowStyle()
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var resetButtonSection: some View {
        // 重置按钮 - 重置当前图片的所有设置为默认值
        Button {
            withAnimation(.easeInOut(duration: 0.3)) {
                // 关闭所有打开的下拉功能行
                customDisplayExpanded = false

                // 重置模型中的设置
                model.imageManager.resetImageSettings(for: currentImageName)

                // 重置自定义显示配置为默认状态
                let defaultConfig = CustomTriggerDisplay()
                model.triggerManager.setCustomTriggerDisplay(for: currentImageName, config: defaultConfig)

                // 重置音效配置
                model.imageMultiSounds.removeValue(forKey: currentImageName)

                // 重置音效播放模式和回溯设置，使用正确的上下文
                var settings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
                settings.soundPlayMode = .simultaneous
                settings.enableBacktrack = false
                settings.backtrackDuration = nil
                settings.autoTriggerInterval = 2.0
                model.imageManager.updateImageSettings(for: currentImageName, in: currentModeContext, settings: settings)

                // 同步更新本地状态
                triggerMode = model.triggerManager.getTriggerMode(for: currentImageName, imageManager: model.imageManager)
                selectedSounds.removeAll()
                soundPlayMode = .simultaneous
                enableBacktrack = false
                backtrackDuration = nil
                showingBacktrackControl = false
                autoTriggerInterval = 2.0

                // 注意：不重置触发次数，因为有单独的重置按钮
                // currentTriggerCount 保持当前值不变

                // 清空暂存状态
                tempCroppedImage = nil
                tempScale = 1.0
                tempOffset = .zero
                tempFileURL = nil

                // 清除UserDefaults中保存的颜色选择
                UserDefaults.standard.removeObject(forKey: "selectedColors_\(currentImageName)")
                UserDefaults.standard.removeObject(forKey: "colorIndex_\(currentImageName)")
            }
        } label: {
            HStack(alignment: .center) {
                // 左侧图标和文本组
                HStack(spacing: AppTheme.smallPadding) {
                    Image(systemName: "arrow.counterclockwise")
                        .foregroundColor(AppTheme.warningColor)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                    
                    Text("重置")
                        .font(.appBody)
                        .foregroundColor(.white)
                }
                
                Spacer()
            }
            .actionRowStyle(.warning)
        }
        .buttonStyle(PlainButtonStyle())
    }

    /// 删除Mode功能区域（危险操作，放在最底部）
    private var deleteModeSection: some View {
        Button(action: { showingDeleteConfirmation = true }) {
            HStack(alignment: .center) {
                HStack(spacing: AppTheme.smallPadding) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                    Text("删除")
                        .font(.appBody)
                        .foregroundColor(.red)
                }
                Spacer()
            }
            .standardRowStyle()
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Private Methods

    /// 视图关闭时自动保存设置
    private func saveSettingsOnClose() {
        // 保存音效设置
        updateImageSounds()

        // 保存其他设置（触发模式、回溯等已经在修改时实时保存）
        // 这里主要是确保音效选择被正确保存

        // 刷新Toast图片缓存，确保使用最新的设置
        model.triggerManager.refreshToastImageCache(for: currentImageName)

        print("图片设置已自动保存: \(currentImageName)")
    }



    /// 另存为Mode（带配置隔离）
    private func copyModeWithIsolation() {
        print("🚀 开始另存为Mode操作: \(currentImageName)")

        // 直接从当前视图状态构建配置，不依赖已保存的配置
        let currentViewSettings = buildCurrentViewSettings()
        print("🔍 当前视图配置 - enableBacktrack: \(currentViewSettings.enableBacktrack), soundPlayMode: \(currentViewSettings.soundPlayMode), randomHintEnabled: \(currentViewSettings.randomHintEnabled)")
        print("🔍 当前视图音效: \(selectedSounds)")

        // 使用自定义的复制方法，直接传递当前视图配置
        if let newModeName = cloneModeWithCurrentViewSettings(currentViewSettings) {
            // 验证复制后的新mode配置（使用正确的modeContext）
            let newModeContext = ModeContext(modeId: newModeName)
            let newSettings = model.imageManager.getImageSettings(for: newModeName, in: newModeContext)
            print("🔍 新mode配置验证 - enableBacktrack: \(newSettings.enableBacktrack), soundPlayMode: \(newSettings.soundPlayMode), randomHintEnabled: \(newSettings.randomHintEnabled)")
            let newSounds = model.soundManager.imageMultiSounds[newModeName] ?? []
            print("🔍 新mode音效列表: \(newSounds)")

            // 播放成功反馈
            WKInterfaceDevice.current().play(.success)
            print("✅ Mode已复制: \(currentImageName) → \(newModeName)")
        } else {
            // 复制失败，播放错误反馈
            WKInterfaceDevice.current().play(.failure)
            print("❌ Mode复制失败: \(currentImageName)")
        }
    }

    /// 从当前视图状态构建ImageSettings配置
    private func buildCurrentViewSettings() -> ImageSettings {
        print("🔄 构建当前视图配置")

        // 获取基础配置作为模板，使用正确的上下文
        var settings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)

        // 应用当前视图中的所有状态
        settings.triggerMode = triggerMode
        settings.soundPlayMode = soundPlayMode
        settings.enableBacktrack = enableBacktrack
        settings.backtrackDuration = backtrackDuration
        settings.autoTriggerInterval = autoTriggerInterval
        settings.randomHintEnabled = randomHintEnabled

        // 应用自定义触发显示配置
        if let cachedConfig = cachedCustomTriggerDisplay {
            settings.customTriggerDisplay = cachedConfig
        }

        print("✅ 当前视图配置构建完成")
        return settings
    }

    /// 使用当前视图配置复制Mode
    private func cloneModeWithCurrentViewSettings(_ currentSettings: ImageSettings) -> String? {
        guard let sourceIndex = model.defaultImages.firstIndex(of: currentImageName) else { return nil }

        // 生成新的唯一mode名称
        let timestamp = Int(Date().timeIntervalSince1970)
        let newModeName = "\(currentImageName)_copy_\(timestamp)"
        print("📝 生成新mode名称: \(newModeName)")

        // 创建新的mode上下文
        let newModeContext = ModeContext(modeId: newModeName)

        // 直接使用当前视图配置创建新mode配置
        var newSettings = currentSettings
        newSettings.modeContext = newModeContext
        newSettings.displayName = (newSettings.displayName.isEmpty ? currentImageName : newSettings.displayName) + " 副本"
        newSettings.clickCount = 0 // 重置累计次数
        newSettings.currentImageIndex = 0 // 重置图片索引，从第一张开始

        // 确保图片序列设置正确
        if newSettings.imageSequence.isEmpty {
            newSettings.imageSequence = [currentImageName]
            newSettings.modeType = .single
        }

        print("📝 准备保存配置 - enableBacktrack: \(newSettings.enableBacktrack), randomHintEnabled: \(newSettings.randomHintEnabled)")
        print("📝 图片序列配置 - imageSequence: \(newSettings.imageSequence), modeType: \(newSettings.modeType)")
        print("📝 显示名称配置 - displayName: '\(newSettings.displayName)', clickCount: \(newSettings.clickCount)")
        print("📝 多图片配置 - navigationMode: \(newSettings.navigationMode), autoSwitchInterval: \(newSettings.autoSwitchInterval), currentImageIndex: \(newSettings.currentImageIndex)")
        print("📝 触发配置 - triggerMode: \(newSettings.triggerMode), showClickCount: \(newSettings.showClickCount)")
        print("📝 音效配置 - soundPlayMode: \(newSettings.soundPlayMode), soundConfigs数量: \(newSettings.soundConfigs.count)")

        // 先复制音效配置（在保存主配置之前）
        model.soundManager.setMultiSoundNames(for: newModeName, soundNames: Array(selectedSounds))
        print("📝 音效配置已设置: \(Array(selectedSounds))")

        // 复制每个音效的详细配置（音量、播放速率等）
        print("📝 开始复制音效详细配置...")
        for soundName in selectedSounds {
            if let soundConfig = currentSettings.soundConfigs[soundName] {
                // 复制音效配置到新mode
                newSettings.soundConfigs[soundName] = soundConfig
                print("📝 复制音效配置: \(soundName) - 音量: \(soundConfig.volume), 播放速率: \(soundConfig.playbackRate)")
            } else {
                print("📝 音效 \(soundName) 没有找到详细配置，将使用默认配置")
            }
        }
        print("📝 音效详细配置复制完成")

        // 复制自定义触发显示配置（在保存主配置之前）
        if let cachedConfig = cachedCustomTriggerDisplay {
            model.triggerManager.setCustomTriggerDisplay(for: newModeName, config: cachedConfig)
            print("📝 自定义触发显示配置已设置")
        }

        // 最后保存主配置，使用同步保存确保立即写入
        model.imageManager.updateImageSettings(for: newModeName, in: newModeContext, settings: newSettings)
        // 强制同步保存，确保配置立即写入存储
        DataService.shared.saveImageSettingsSync(newSettings, for: newModeName, in: newModeContext)
        print("📝 主配置已同步保存")

        // 强制更新缓存，确保后续读取能获取到正确的配置
        model.imageManager.forceUpdateCache(for: newModeName, in: newModeContext, settings: newSettings)
        print("📝 缓存已强制更新")

        // 复制缩放和偏移
        let originalScale = model.imageManager.getImageScale(for: currentImageName)
        let originalOffset = model.imageManager.getImageOffset(for: currentImageName)
        model.imageManager.setImageScale(for: newModeName, scale: originalScale)
        model.imageManager.setImageOffset(for: newModeName, offset: originalOffset)
        print("📝 缩放和偏移已复制")

        // 添加到图片列表
        model.defaultImages.insert(newModeName, at: sourceIndex + 1)
        model.saveImageOrder()

        // 刷新缓存
        DispatchQueue.main.async {
            self.model.imageManager.objectWillChange.send()
        }

        print("✅ Mode复制完成: \(newModeName)")
        return newModeName
    }

    /// 保存当前视图中的所有配置到原mode
    private func saveCurrentViewConfiguration() {
        print("🔄 开始保存当前视图配置到原mode: \(currentImageName)")

        // 保存音效选择
        updateImageSounds()
        print("📝 已更新音效选择: \(selectedSounds)")

        // 获取当前存储的配置，使用正确的上下文
        let originalSettings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
        print("📖 原始配置 - enableBacktrack: \(originalSettings.enableBacktrack), soundPlayMode: \(originalSettings.soundPlayMode)")

        // 保存当前视图中的所有开关状态和配置
        var settings = originalSettings

        // 保存触发模式
        settings.triggerMode = triggerMode
        print("📝 设置触发模式: \(triggerMode)")

        // 保存音效播放模式
        settings.soundPlayMode = soundPlayMode
        print("📝 设置音效播放模式: \(soundPlayMode)")

        // 保存回溯相关配置
        settings.enableBacktrack = enableBacktrack
        settings.backtrackDuration = backtrackDuration
        print("📝 设置回溯配置 - enableBacktrack: \(enableBacktrack), backtrackDuration: \(backtrackDuration ?? -1)")

        // 保存自动触发间隔
        settings.autoTriggerInterval = autoTriggerInterval
        print("📝 设置自动触发间隔: \(autoTriggerInterval)")

        // 保存随机提示开关
        settings.randomHintEnabled = randomHintEnabled
        print("📝 设置随机提示开关: \(randomHintEnabled)")

        // 保存自定义触发显示配置
        if let cachedConfig = cachedCustomTriggerDisplay {
            settings.customTriggerDisplay = cachedConfig
            // 同时保存到TriggerManager
            model.triggerManager.setCustomTriggerDisplay(for: currentImageName, config: cachedConfig)
            print("📝 设置自定义触发显示配置")
        }

        // 立即保存到ImageManager，使用正确的上下文
        model.imageManager.updateImageSettings(for: currentImageName, in: currentModeContext, settings: settings)

        // 验证保存结果，使用正确的上下文
        let savedSettings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
        print("✅ 保存后验证 - enableBacktrack: \(savedSettings.enableBacktrack), soundPlayMode: \(savedSettings.soundPlayMode)")
        print("✅ 已保存当前视图配置到原mode: \(currentImageName)")
    }

    /// 删除Mode并关闭设置视图
    private func deleteModeAndClose() {
        guard let index = model.defaultImages.firstIndex(of: currentImageName) else { return }

        // 删除关联数据
        model.imageManager.deleteImage(currentImageName)
        model.soundManager.removeSoundsForImage(currentImageName)
        model.triggerManager.removeTriggerSettings(for: currentImageName)

        // 从列表中移除
        model.defaultImages.remove(at: index)

        // 如果删除当前选中图片，切换到第一张
        if model.selectedDefaultImageName == currentImageName {
            model.selectedDefaultImageName = model.defaultImages.first ?? ""
        }

        // 保存顺序
        model.saveImageOrder()

        // 播放反馈
        WKInterfaceDevice.current().play(.success)

        print("Mode已删除: \(currentImageName)")

        // 关闭设置视图
        isPresented = false
    }

    private func updateImageSounds() {
        if selectedSounds.isEmpty {
            model.imageMultiSounds.removeValue(forKey: currentImageName)
        } else {
            // 根据用户选择顺序保存音效数组，确保所有选中的音效都被保存
            let orderedFromSelection = model.selectedSoundsOrder.filter { selectedSounds.contains($0) }
            let remainingSelected = selectedSounds.filter { !model.selectedSoundsOrder.contains($0) }
            let finalOrdered = orderedFromSelection + Array(remainingSelected)
            model.imageMultiSounds[currentImageName] = finalOrdered

            print("保存音效配置: \(currentImageName) -> \(finalOrdered)")
        }
    }
    
    private func getSortedSounds() -> [String] {
        // 如果是顺序播放模式，按照selectedSoundsOrder的顺序排列
        if model.soundPlayMode == .sequential {
            // 过滤出已选择的音效，并按照顺序排列
            return model.selectedSoundsOrder.filter { selectedSounds.contains($0) }
        } else {
            // 同时播放模式，按照字母顺序排列
            return Array(selectedSounds).sorted()
        }
    }
    

    
    // 将已选中的音效转换为顺序播放模式 - 与音频列表视图保持一致
    private func convertSelectedSoundsToSequential() {
        model.sequentialSoundOrder.removeAll()
        model.nextSequenceNumber = 1
        
        // 获取当前选中音效的顺序（基于现有的selectedSoundsOrder）
        var currentOrder: [String] = []
        
        // 首先添加已经在selectedSoundsOrder中的音效
        for sound in model.selectedSoundsOrder {
            if selectedSounds.contains(sound) {
                currentOrder.append(sound)
            }
        }
        
        // 添加不在selectedSoundsOrder中的新音效
        for sound in selectedSounds {
            if !currentOrder.contains(sound) {
                currentOrder.append(sound)
            }
        }
        
        // 按照当前顺序分配序号
        for sound in currentOrder {
                model.sequentialSoundOrder[sound] = model.nextSequenceNumber
                model.nextSequenceNumber += 1
            }
        
        // 将当前顺序同步到model.selectedSoundsOrder
        // 先移除旧的记录
        model.selectedSoundsOrder = model.selectedSoundsOrder.filter { !selectedSounds.contains($0) }
        // 添加新的顺序
        model.selectedSoundsOrder = currentOrder + model.selectedSoundsOrder
        
        // 更新图片关联的音效以保持正确顺序
        updateImageSounds()
    }

    // MARK: - Backtrack Helper Methods

    /// 获取当前选中音效的总时长
    private func getSoundTotalDuration() -> TimeInterval {
        guard selectedSounds.count == 1,
              let soundName = selectedSounds.first else {
            print("🎵 getSoundTotalDuration: 非单音效模式，返回默认时长 1.0s")
            return 1.0 // 默认时长
        }

        // 从SoundManager获取音效时长
        let duration = model.soundManager.getSoundDuration(for: soundName)
        print("🎵 getSoundTotalDuration: \(soundName) = \(duration)s")
        return duration
    }

    /// 显示当前回溯时长的文本
    private func backtrackDisplayText() -> String {
        let totalDuration = getSoundTotalDuration()
        let duration = backtrackDuration ?? totalDuration
        return String(format: "%.1fs", duration)
    }

    /// 根据音效总时长计算滑杆步长（20档左右）
    private func backtrackStep() -> Double {
        let totalDuration = getSoundTotalDuration()
        let step = totalDuration / 20
        return min(1.5, max(0.1, step))
    }

    /// 保存回溯时长到ImageSettings
    private func saveBacktrackDuration() {
        var settings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
        settings.backtrackDuration = backtrackDuration
        model.imageManager.updateImageSettings(for: currentImageName, in: currentModeContext, settings: settings)
    }
    
    // 获取触发模式文本
    private func getTriggerModeText() -> String {
        switch triggerMode {
        case .tap:
            return "点击"
        case .shake:
            return "摇晃"
        case .auto:
            return "自动"
        }
    }
    
    // 获取触发模式图标
    private func getTriggerModeIcon() -> String {
        switch triggerMode {
        case .tap:
            return "hand.tap.fill"
        case .shake:
            return "iphone.radiowaves.left.and.right"
        case .auto:
            return "a"
        }
    }
    
    // 裁剪框大小
    private var cropFrameSize: CGSize {
        // 获取屏幕宽度
        let screenWidth = WKInterfaceDevice.current().screenBounds.width
        // 使用整个屏幕作为裁剪区域
        return CGSize(width: screenWidth, height: screenWidth)
    }
    
    // 裁剪图片
    private func cropImage() {
        // 设置裁剪状态
        isCropping = true
        
        // 延迟执行以显示加载指示器
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 创建裁剪视图包装器
            let wrapper = SimpleImageEditorViewWrapper()
            
            // 执行裁剪
            _ = wrapper.cropImage(
                imageName: self.imageName,
                cropSize: self.cropFrameSize
            )
            
            // 结束裁剪状态
            self.isCropping = false
        }
    }
    
    // 加载和启动颜色动画 - 优化版本，避免主线程hang
    private func loadPreviewColors() {
        // 先尝试从UserDefaults加载颜色配置
        if let colorData = UserDefaults.standard.data(forKey: "selectedColors_\(currentImageName)"),
           let selectedColors = try? JSONDecoder().decode([String].self, from: colorData),
           !selectedColors.isEmpty {

            if selectedColors.contains("rainbow") {
                self.previewColors = [.red, .orange, .yellow, .green, .blue, .purple, .pink, .cyan, .mint]
            } else {
                self.previewColors = selectedColors.map { AppTheme.getColor(fromName: $0) }
            }
        } else {
            // 使用缓存的配置或默认颜色，避免同步数据加载
            if let cachedConfig = cachedCustomTriggerDisplay {
                self.previewColors = [cachedConfig.getColor()]
            } else {
                // 使用默认白色，避免触发数据加载
                self.previewColors = [.white]

                // 在后台异步加载真实配置
                DispatchQueue.global(qos: .userInitiated).async {
                    let config = model.triggerManager.getCustomTriggerDisplay(for: currentImageName)
                    DispatchQueue.main.async {
                        self.cachedCustomTriggerDisplay = config
                        self.previewColors = [config.getColor()]
                    }
                }
            }
        }
    }
    
    private func startColorAnimation() {
        // 先停止旧的定时器，防止内存泄漏
        colorChangeTimer?.invalidate()
        
        // 重新加载颜色配置
        loadPreviewColors()
        
        // 只有当颜色多于一种时，才启动动画
        if previewColors.count > 1 {
            colorChangeTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
                withAnimation(.easeInOut) {
                    // 安全地更新索引
                    currentColorIndex = (currentColorIndex + 1) % previewColors.count
                }
            }
        } else {
            // 如果只有一种或没有颜色，将索引重置为0，并停止定时器
            currentColorIndex = 0
            colorChangeTimer?.invalidate()
        }
    }
    
    private func stopColorAnimation() {
        colorChangeTimer?.invalidate()
        colorChangeTimer = nil
    }
}

// MARK: - Play Mode Section
private extension ImageSettingsView {
    var playModeSection: some View {
        Button(action: cycleSoundPlayMode) {
            HStack {
                Image(systemName: soundPlayMode.icon)
                    .font(.system(size: AppTheme.smallIconSize))
                    .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                    .foregroundColor(AppTheme.primaryColor)

                Text("播放模式")
                    .font(AppTheme.bodyFont)
                    .foregroundColor(Color.textPrimary)

                Spacer()

                Text(soundPlayMode.rawValue)
                    .font(AppTheme.smallFont)
                    .foregroundColor(AppTheme.tertiaryTextColor)
                    .padding(.horizontal)
            }
            .standardRowStyle()
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func cycleSoundPlayMode() {
        withAnimation(.standardAnimation()) {
            switch soundPlayMode {
            case .sequential:
                soundPlayMode = .random
            case .random:
                soundPlayMode = .simultaneous
            case .simultaneous:
                soundPlayMode = .sequential
            }

            // 保存到ImageSettings，使用正确的上下文
            var settings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
            settings.soundPlayMode = soundPlayMode
            model.imageManager.updateImageSettings(for: currentImageName, in: currentModeContext, settings: settings)
        }
    }
    
    // 回溯功能开关部分
    var backtrackSection: some View {
        Button(action: {
            enableBacktrack.toggle()
            // 保存到ImageSettings，使用正确的上下文
            var settings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
            settings.enableBacktrack = enableBacktrack
            model.imageManager.updateImageSettings(for: currentImageName, in: currentModeContext, settings: settings)

            // 如果关闭回溯，同时关闭回溯控制展开状态
            if !enableBacktrack {
                showingBacktrackControl = false
            }
        }) {
            HStack(alignment: .center) {
                // 左侧图标和文本组
                HStack(spacing: AppTheme.smallPadding) {
                    Image(systemName: "arrow.trianglehead.counterclockwise")
                        .foregroundColor(AppTheme.primaryColor)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                    
                    Text("音效回溯")
                        .font(.appBody)
                        .foregroundColor(Color.textPrimary)
                }
                
                Spacer()
                
                // 右侧开关
                Toggle("", isOn: $enableBacktrack)
                    .labelsHidden()
                    .allowsHitTesting(false) // 防止Toggle自身接收点击事件
            }
            .standardRowStyle()
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 音效回溯功能行
    var backtrackDurationSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.smallPadding) {
            Button(action: {
                withAnimation(.easeInOut(duration: AppConfig.defaultAnimationDuration)) {
                    showingBacktrackControl.toggle()
                }
            }) {
                HStack {
                    HStack(spacing: AppTheme.smallPadding) {
                        Image(systemName: "arrow.trianglehead.counterclockwise")
                            .foregroundColor(AppTheme.primaryColor)
                            .font(.system(size: AppTheme.smallIconSize))
                            .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                        Text("回溯时长")
                            .font(.appBody)
                            .foregroundColor(Color.textPrimary)
                    }
                    Spacer()
                    Text(backtrackDisplayText())
                        .font(.appSmall)
                        .foregroundColor(Color.gray)
                    Image(systemName: showingBacktrackControl ? "chevron.up" : "chevron.down")
                        .font(.appSmall)
                        .foregroundColor(Color.gray)
                }
                .standardRowStyle()
            }
            .buttonStyle(PlainButtonStyle())

            if showingBacktrackControl {
                VStack(spacing: AppTheme.smallPadding) {
                    Slider(
                        value: Binding<Double>(
                            get: {
                                let totalDuration = getSoundTotalDuration()
                                return backtrackDuration ?? totalDuration
                            },
                            set: { newVal in
                                let totalDuration = getSoundTotalDuration()
                                let clampedVal = min(newVal, totalDuration) // 确保不超过总时长
                                print("🎵 回溯时长滑杆调整: \(newVal) -> \(clampedVal) (总时长: \(totalDuration))")

                                if abs(clampedVal - totalDuration) < 0.05 {
                                    backtrackDuration = nil // 到开头
                                    print("🎵 回溯设置为: 到开头")
                                } else if clampedVal <= backtrackStep() / 2 {
                                    backtrackDuration = 0 // 不回溯
                                    print("🎵 回溯设置为: 不回溯")
                                } else {
                                    backtrackDuration = clampedVal
                                    print("🎵 回溯设置为: \(clampedVal)s")
                                }
                                saveBacktrackDuration()
                            }
                        ),
                        in: 0...max(getSoundTotalDuration(), 0.1),
                        step: backtrackStep()
                    )
                    .accentColor(AppTheme.primaryColor)
                    HStack {
                        Text("0s")
                            .font(.appLabel)
                            .foregroundColor(Color.gray)
                        Spacer()
                        Text("\(String(format: "%.1f", getSoundTotalDuration()))s")
                            .font(.appLabel)
                            .foregroundColor(Color.gray)
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, AppTheme.smallPadding)
                .cornerRadius(AppTheme.cornerRadius)
                .animation(.spring(response: 0.35, dampingFraction: 0.8), value: showingBacktrackControl)
            }
        }
    }

    // MARK: - Auto Trigger Interval Section
    private var autoTriggerIntervalSection: some View {
        VStack(spacing: AppTheme.smallPadding) {
            // 使用反向映射：左侧慢(10.0秒) -> 右侧快(0.5秒)
            Slider(
                value: Binding(
                    get: { 10.5 - autoTriggerInterval }, // 将0.5-10.0映射为10.0-0.5
                    set: { autoTriggerInterval = 10.5 - $0 } // 反向映射回去
                ),
                in: 0.5...10.0,
                step: 0.5,
                onEditingChanged: { editing in
                    if !editing {
                        // 当用户停止拖拽滑块时保存设置
                        updateAutoTriggerInterval()
                    }
                }
            )
            .accentColor(AppTheme.primaryColor)
            .onChange(of: autoTriggerInterval) { _, newValue in
                // 实时保存设置变化
                updateAutoTriggerInterval()
            }

            HStack {
                Text("慢")
                    .font(.appLabel)
                    .foregroundColor(Color.gray)
                Spacer()
                Text("快")
                    .font(.appLabel)
                    .foregroundColor(Color.gray)
            }
        }
        .padding(.horizontal)
        .padding(.vertical, AppTheme.smallPadding)
        .cornerRadius(AppTheme.cornerRadius)
        .animation(.spring(response: 0.35, dampingFraction: 0.8), value: triggerMode == .auto)
    }

    // 更新自动触发时间间隔到模型
    private func updateAutoTriggerInterval() {
        var settings = model.imageManager.getImageSettings(for: currentImageName, in: currentModeContext)
        settings.autoTriggerInterval = autoTriggerInterval
        model.imageManager.updateImageSettings(for: currentImageName, in: currentModeContext, settings: settings)

        // 发送通知，让FullScreenImageView重新启动自动触发定时器
        NotificationCenter.default.post(
            name: NSNotification.Name("AutoTriggerIntervalChanged"),
            object: nil,
            userInfo: ["imageName": currentImageName, "interval": autoTriggerInterval]
        )
    }
    

    

}

#Preview {
    ImageSettingsView(model: BugOffModel(), imageName: "bug5", isPresented: .constant(true))
} 
