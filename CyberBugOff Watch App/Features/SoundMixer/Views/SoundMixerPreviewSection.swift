import SwiftUI

/// 声音合成页顶部的预览控制区（播放/停止 + 播放模式）
/// 仅负责 UI，不持有任何业务逻辑，通过闭包回调到父视图。
struct SoundMixerPreviewSection: View {
    @ObservedObject var model: BugOffModel
    @EnvironmentObject private var soundManager: SoundManager
    @Binding var selectedSounds: [String]
    @Binding var isPreviewPlaying: Bool
    let togglePreviewPlayback: () -> Void
    let cycleMixingMode: () -> Void
    let saveComposite: () -> Void
    
    var body: some View {
        Group {
            if !selectedSounds.isEmpty {
                previewPlaybackButton
            }
            mixingModeButton
            if !selectedSounds.isEmpty {
                saveButton
            }
        }
    }
}

// MARK: - 子视图
private extension SoundMixerPreviewSection {
    var previewPlaybackButton: some View {
        Button(action: togglePreviewPlayback) {
            HStack(alignment: .center) {
                // 左侧图标和文本组
                HStack(spacing: AppTheme.smallPadding) {
                    Image(systemName: isPreviewPlaying ? "stop.fill" : "play.fill")
                        .foregroundColor(isPreviewPlaying ? AppTheme.warningColor : AppTheme.primaryColor)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                        .scaleEffect(isPreviewPlaying ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: isPreviewPlaying)
                    
                    Text(isPreviewPlaying ? "停止预览" : "预览播放")
                        .font(.appBody)
                        .foregroundColor(isPreviewPlaying ? AppTheme.warningColor : Color.textPrimary)
                        .animation(.easeInOut(duration: 0.2), value: isPreviewPlaying)
                }
                
                Spacer()
            }
            .previewRowStyle(isActive: isPreviewPlaying)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .listRowInsets(EdgeInsets())
        .listRowBackground(Color.clear)
    }
    
    var mixingModeButton: some View {
        Button(action: cycleMixingMode) {
            HStack {
                Image(systemName: soundManager.soundPlayMode.icon)
                    .font(.system(size: AppTheme.smallIconSize))
                    .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                    .foregroundColor(AppTheme.primaryColor)
                
                Text("合成模式")
                    .font(AppTheme.bodyFont)
                
                Spacer()
                
                Text(soundManager.soundPlayMode.rawValue)
                    .font(AppTheme.smallFont)
                    .foregroundColor(AppTheme.tertiaryTextColor)
                    .padding(.horizontal)
            }
            .standardRowStyle()
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .listRowInsets(EdgeInsets())
        .listRowBackground(Color.clear)
    }
    
    var saveButton: some View {
        Button(action: saveComposite) {
            HStack {
                Image(systemName: "square.and.arrow.down")
                    .font(.system(size: AppTheme.smallIconSize))
                    .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                    .foregroundColor(AppTheme.primaryColor)
                Text("保存合成")
                    .font(AppTheme.bodyFont)
                Spacer()
            }
            .standardRowStyle()
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .listRowInsets(EdgeInsets())
        .listRowBackground(Color.clear)
    }
} 
