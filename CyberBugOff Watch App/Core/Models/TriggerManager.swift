import Foundation
import SwiftUI
import CoreMotion

// MARK: - Trigger Manager
class TriggerManager: ObservableObject {
    // MARK: - Published Properties
    @Published var customTriggerDisplays: [String: CustomTriggerDisplay] = [:]
    
    // MARK: - Private Properties
    private let dataService = DataService.shared
    private var colorIndices: [String: Int] = [:]
    
    // Toast 图片缓存 - 避免重复处理
    private static let toastImageCache: NSCache<NSString, UIImage> = {
        let cache = NSCache<NSString, UIImage>()
        cache.countLimit = 30 // 缓存30张toast图片
        cache.totalCostLimit = 5 * 1024 * 1024 // 5MB限制
        return cache
    }()
    
    // 预加载任务跟踪，防止重复预加载
    private var preloadingTasks: Set<String> = []
    private let preloadQueue = DispatchQueue(label: "toast.preload", qos: .utility)
    
    // MARK: - Initialization
    init() {
        loadData()
    }
    
    // MARK: - Public Methods
    
    /// Get custom trigger display configuration for image
    public func getCustomTriggerDisplay(for imageName: String) -> CustomTriggerDisplay {
        let config = customTriggerDisplays[imageName] ?? CustomTriggerDisplay()
        return config
    }

    /// 检查指定mode的配置是否已缓存
    func isConfigurationCached(for imageName: String) -> Bool {
        return customTriggerDisplays[imageName] != nil
    }
    
    /// Set custom trigger display configuration for image
    func setCustomTriggerDisplay(for imageName: String, config: CustomTriggerDisplay) {
        // 获取现有配置
        var existingConfig = getCustomTriggerDisplay(for: imageName)

        // 根据新配置的显示模式，只更新对应模式的设置
        switch config.displayMode {
        case .text:
            // 更新文字模式相关的设置
            existingConfig.displayMode = config.displayMode
            existingConfig.isEnabled = config.isEnabled
            existingConfig.customText = config.customText
            existingConfig.incrementValue = config.incrementValue
            existingConfig.displayColor = config.displayColor
            existingConfig.emoji = config.emoji
            existingConfig.animationStyle = config.animationStyle // 文字模式的动画样式
            existingConfig.showIncrement = config.showIncrement
            existingConfig.fontSize = config.fontSize

        case .image:
            // 更新图片模式相关的设置
            existingConfig.displayMode = config.displayMode
            existingConfig.isEnabled = config.isEnabled
            existingConfig.imageContentMode = config.imageContentMode
            existingConfig.imageSize = config.imageSize
            existingConfig.imageOpacity = config.imageOpacity
            existingConfig.imageAnimationStyle = config.imageAnimationStyle // 图片模式的动画样式
            existingConfig.customImageURL = config.customImageURL

            // 更新自定义图片相关的配置
            existingConfig.customImageScale = config.customImageScale
            existingConfig.customImageOffset = config.customImageOffset
            existingConfig.customCropRect = config.customCropRect
            existingConfig.customCropPath = config.customCropPath
            existingConfig.circleSelectionData = config.circleSelectionData

            // 更新主图圈选相关的配置
            existingConfig.mainCircleSelectionData = config.mainCircleSelectionData
            existingConfig.mainImageScale = config.mainImageScale
            existingConfig.mainImageOffset = config.mainImageOffset


        }

        // 保存合并后的配置
        customTriggerDisplays[imageName] = existingConfig
        dataService.saveCustomTriggerDisplay(existingConfig, for: imageName)
    }
    
    /// Check if custom trigger display is enabled for image
    /// 现在默认总是启用自定义显示
    func isCustomTriggerDisplayEnabled(for imageName: String) -> Bool {
        return true  // 总是启用自定义显示
    }
    
    /// Get custom trigger display text for image
    func getCustomTriggerText(for imageName: String, currentCount: Int) -> String {
        let config = getCustomTriggerDisplay(for: imageName)
        return config.getDisplayText(currentCount: currentCount)
    }
    
    /// 应用圈选裁剪到全屏图片（公共方法，供FullScreenImageView使用）
    public func applyCircleSelectionToFullscreen(_ image: UIImage, selectionData: CircleSelectionData, scale: CGFloat, offset: CGSize) -> UIImage? {
        print("🔄 FullScreenView应用圈选裁剪，路径点数: \(selectionData.pathPoints.count)")
        
        // 直接使用圈选路径，不再进行二次优化
        let optimizedPoints = selectionData.pathPoints
        
        // 检查优化后的点数是否足够
        if optimizedPoints.count >= 3 {
            let optimizedSelectionData = CircleSelectionData(pathPoints: optimizedPoints)
            if let croppedImage = applyCircleSelectionToImage(image, selectionData: optimizedSelectionData, scale: scale, offset: offset) {
                print("✅ 全屏视图圈选裁剪成功")
                return croppedImage
            } else {
                print("⚠️ 全屏视图圈选裁剪失败，使用原图")
                return image
            }
        } else {
            print("⚠️ 优化后点数不足(\(optimizedPoints.count))，使用原图")
            return image
        }
    }
    
    /// 刷新指定图片的Toast缓存
    public func refreshToastImageCache(for imageName: String) {
        // 简单方法：直接清除整个缓存
        TriggerManager.toastImageCache.removeAllObjects()
        print("🗑️ 已清除所有Toast图片缓存")

        // 发送通知，让Toast视图重新加载图片
        NotificationCenter.default.post(name: NSNotification.Name("ToastImageCacheCleared"), object: nil)
        print("📢 已发送Toast缓存清除通知")

        // 预加载新的Toast图片
        preloadCustomDisplayImage(for: imageName)
    }

    /// Get custom display image for toast (独立于全屏视图的图片)
    func getCustomDisplayImage(for imageName: String) -> UIImage? {
        print("🖼️ 获取Toast图片: \(imageName)")
        let config = getCustomTriggerDisplay(for: imageName)
        
        // 生成缓存键
        let cacheKey = generateToastImageCacheKey(for: imageName, config: config)
        
        // 先检查缓存
        if let cachedImage = TriggerManager.toastImageCache.object(forKey: cacheKey as NSString) {
            print("✅ 使用缓存的Toast图片")
            return cachedImage
        }
        
        // 如果有自定义裁剪的图片URL，优先使用（传统模式）
        if let urlString = config.customImageURL {
            print("📁 尝试加载自定义图片路径: \(urlString)")

            // 创建文件URL（支持文件路径）
            let url: URL
            if urlString.hasPrefix("file://") {
                // 已经是完整的URL
                guard let parsedURL = URL(string: urlString) else {
                    print("❌ 无法解析自定义图片URL: \(urlString)")
                    return nil
                }
                url = parsedURL
            } else {
                // 是文件路径，需要转换为文件URL
                url = URL(fileURLWithPath: urlString)
            }

            print("📁 文件URL: \(url)")

            do {
                let data = try Data(contentsOf: url)
                print("📁 数据加载成功，大小: \(data.count) bytes")

                if let image = UIImage(data: data) {
                    // 检查是否需要应用圈选裁剪到自定义图片
                    var finalImage = image
                    if let selectionData = config.circleSelectionData {
                        print("🔄 对自定义图片应用圈选数据，原始点数: \(selectionData.pathPoints.count)")

                        // 应用圈选裁剪到自定义图片
                        if let croppedImage = applyCircleSelectionToImage(
                            image,
                            selectionData: selectionData,
                            scale: config.customImageScale,
                            offset: config.customImageOffset
                        ) {
                            print("✅ 自定义图片圈选裁剪成功，尺寸: \(croppedImage.size)")
                            finalImage = croppedImage
                        } else {
                            print("❌ 自定义图片圈选裁剪失败，使用原图")
                        }
                    }

                    // Apply toast-specific compression for performance
                    let compressedImage = ImageCompressionUtils.compressForToast(finalImage) ?? finalImage
                    // 缓存结果
                    TriggerManager.toastImageCache.setObject(compressedImage, forKey: cacheKey as NSString)
                    return compressedImage
                } else {
                    print("❌ 无法从数据创建UIImage")
                }
            } catch {
                print("❌ 加载自定义图片数据失败: \(error)")
            }
        }

        // 获取原始图片
        guard let originalImage = getOriginalImage(for: imageName) else {
            print("❌ 无法获取原始图片: \(imageName)")
            return nil
        }
        
        print("📸 原始图片尺寸: \(originalImage.size)")
        var finalImage: UIImage = originalImage

        // 如果有圈选数据，应用路径裁剪（优化处理）
        if let selectionData = config.circleSelectionData {
            print("🔄 处理圈选数据，原始点数: \(selectionData.pathPoints.count)")
            
            // 直接使用圈选路径，不再进行二次优化
            let optimizedPoints = selectionData.pathPoints
            
            // 检查优化后的点数是否足够
            if optimizedPoints.count >= 3 {
                let optimizedSelectionData = CircleSelectionData(pathPoints: optimizedPoints)
                if let croppedImage = applyCircleSelectionToImage(originalImage, selectionData: optimizedSelectionData, scale: config.customImageScale, offset: config.customImageOffset) {
                    finalImage = croppedImage
                    print("✅ 圈选裁剪成功")
                } else {
                    print("⚠️ 圈选裁剪失败，使用原图")
                    finalImage = originalImage
                }
            } else {
                print("⚠️ 优化后点数不足(\(optimizedPoints.count))，使用原图")
                finalImage = originalImage
            }
        }
        // 如果有圈选裁剪配置，应用裁剪效果（向后兼容）
        else if let cropRect = config.customCropRect {
            print("✂️ 应用传统裁剪")
            finalImage = applyCropToImage(originalImage, cropRect: cropRect, scale: config.customImageScale, offset: config.customImageOffset) ?? originalImage
        }
        
        print("🎯 最终图片尺寸: \(finalImage.size)")

        // Apply toast-specific compression for optimal performance
        let compressedImage = ImageCompressionUtils.compressForToast(finalImage) ?? finalImage
        
        // 验证压缩后的图片
        if compressedImage.size.width > 0 && compressedImage.size.height > 0 {
            // 缓存处理结果
            let imageData = compressedImage.pngData()
            let cost = imageData?.count ?? 0
            TriggerManager.toastImageCache.setObject(compressedImage, forKey: cacheKey as NSString, cost: cost)
            print("💾 Toast图片已缓存，尺寸: \(compressedImage.size)")
            return compressedImage
        } else {
            print("❌ 压缩后图片无效，返回原图")
            return finalImage
        }
    }
    
    /// 生成Toast图片缓存键
    private func generateToastImageCacheKey(for imageName: String, config: CustomTriggerDisplay) -> String {
        var keyComponents = [imageName]
        
        // 包含圈选数据的哈希
        if let selectionData = config.circleSelectionData {
            let pointsHash = selectionData.pathPoints.reduce(0) { result, point in
                result ^ point.x.hashValue ^ point.y.hashValue
            }
            keyComponents.append("circle_\(pointsHash)")
        }
        
        // 包含裁剪矩形
        if let cropRect = config.customCropRect {
            keyComponents.append("crop_\(cropRect.origin.x)_\(cropRect.origin.y)_\(cropRect.width)_\(cropRect.height)")
        }
        
        // 包含缩放和偏移
        keyComponents.append("scale_\(config.customImageScale)")
        keyComponents.append("offset_\(config.customImageOffset.width)_\(config.customImageOffset.height)")
        
        return keyComponents.joined(separator: "_")
    }

    /// Get original image without any cropping effects
    private func getOriginalImage(for imageName: String) -> UIImage? {
        print("🔍 正在获取原始图片: \(imageName)")

        // 解析实际的图片名称（处理复制mode的情况）
        let actualImageName = resolveActualImageName(for: imageName)
        if actualImageName != imageName {
            print("🔄 复制mode解析: \(imageName) -> \(actualImageName)")
        }

        // 首先尝试从 DataService 获取用户添加的图片路径
        if let userAddedImages = dataService.loadUserAddedImages(),
           let url = userAddedImages[actualImageName],
           let data = try? Data(contentsOf: url),
           let image = UIImage(data: data) {
            print("✅ 从用户数据加载图片成功: \(actualImageName), 尺寸: \(image.size)")
            return image
        }

        // 然后尝试从 App Bundle 加载
        if let bundleImage = UIImage(named: actualImageName) {
            print("✅ 从Bundle加载图片成功: \(actualImageName), 尺寸: \(bundleImage.size)")
            return bundleImage
        }

        print("❌ 无法加载图片: \(actualImageName) (原始名称: \(imageName))")
        return nil
    }

    /// 解析实际的图片名称（处理复制mode和图片序列）
    private func resolveActualImageName(for imageName: String) -> String {
        // 如果是复制的mode，需要从其配置中获取实际的图片名称
        if imageName.contains("_copy_") {
            let modeContext = ModeContext(modeId: imageName)
            let settings = dataService.loadImageSettings(for: imageName, in: modeContext)

            // 从图片序列中获取实际的图片名称
            if !settings.imageSequence.isEmpty {
                return settings.currentDisplayImageName
            } else {
                // 如果没有图片序列，从mode名称中提取原始图片名称
                return extractOriginalImageName(from: imageName)
            }
        }

        return imageName
    }

    /// 从复制mode名称中提取原始图片名称
    private func extractOriginalImageName(from modeName: String) -> String {
        if let copyIndex = modeName.range(of: "_copy_") {
            return String(modeName[..<copyIndex.lowerBound])
        }
        return modeName
    }

    /// Apply crop configuration to image
    private func applyCropToImage(_ image: UIImage, cropRect: CGRect, scale: CGFloat, offset: CGSize) -> UIImage? {
        let imageSize = image.size

        // 将相对坐标转换为绝对坐标
        let absoluteCropRect = CGRect(
            x: cropRect.origin.x * imageSize.width,
            y: cropRect.origin.y * imageSize.height,
            width: cropRect.size.width * imageSize.width,
            height: cropRect.size.height * imageSize.height
        )

        // 确保裁剪区域在图片范围内
        let clampedCropRect = absoluteCropRect.intersection(CGRect(origin: .zero, size: imageSize))

        guard !clampedCropRect.isEmpty else {
            return image
        }

        // 执行裁剪
        guard let cgImage = image.cgImage,
              let croppedCGImage = cgImage.cropping(to: clampedCropRect) else {
            return image
        }

        return UIImage(cgImage: croppedCGImage)
    }

    /// Apply circle selection to image using path mask
    private func applyCircleSelectionToImage(_ image: UIImage, selectionData: CircleSelectionData, scale: CGFloat, offset: CGSize) -> UIImage? {
        let imageSize = image.size
        print("🎯 开始应用圈选裁剪，原图尺寸: \(imageSize)")
        print("📍 路径点数量: \(selectionData.pathPoints.count)")
        print("🔧 缩放: \(scale), 偏移: \(offset)")

        // 获取屏幕尺寸用于坐标转换
        let screenSize = AppTheme.screenSize

        // 计算图片在屏幕上的实际显示尺寸和位置
        // 使用与SimpleImageEditorView相同的.scaledToFill()逻辑
        let imageAspectRatio = imageSize.width / imageSize.height
        let screenAspectRatio = screenSize.width / screenSize.height

        // 计算.scaledToFill()后的尺寸
        var fillWidth: CGFloat
        var fillHeight: CGFloat

        if imageAspectRatio > screenAspectRatio {
            // 图片比屏幕更宽，高度会匹配屏幕
            fillHeight = screenSize.height
            fillWidth = fillHeight * imageAspectRatio
        } else {
            // 图片比屏幕更高，宽度会匹配屏幕
            fillWidth = screenSize.width
            fillHeight = fillWidth / imageAspectRatio
        }

        // 然后应用用户的缩放比例
        let scaledWidth = fillWidth * scale
        let scaledHeight = fillHeight * scale

        // 计算图片在屏幕上的显示区域（考虑偏移）
        let displayRect = CGRect(
            x: (screenSize.width - scaledWidth) / 2 + offset.width,
            y: (screenSize.height - scaledHeight) / 2 + offset.height,
            width: scaledWidth,
            height: scaledHeight
        )

        print("📐 图片显示信息: \(Int(scaledWidth))x\(Int(scaledHeight))")

        // 将相对坐标转换为图片坐标
        let absolutePathPoints = selectionData.pathPoints.map { relativePoint in
            // 先转换为屏幕坐标
            let screenPoint = CGPoint(
                x: relativePoint.x * screenSize.width,
                y: relativePoint.y * screenSize.height
            )

            // 转换为相对于图片显示区域的坐标
            let imageRelativePoint = CGPoint(
                x: (screenPoint.x - displayRect.minX) / displayRect.width,
                y: (screenPoint.y - displayRect.minY) / displayRect.height
            )

            // 最后转换为图片像素坐标
            let imagePixelPoint = CGPoint(
                x: imageRelativePoint.x * imageSize.width,
                y: imageRelativePoint.y * imageSize.height
            )



            return imagePixelPoint
        }

        print("🔄 转换后的路径点范围:")
        if let minX = absolutePathPoints.map({ $0.x }).min(),
           let maxX = absolutePathPoints.map({ $0.x }).max(),
           let minY = absolutePathPoints.map({ $0.y }).min(),
           let maxY = absolutePathPoints.map({ $0.y }).max() {
            print("   X: \(minX) ~ \(maxX) (图片宽度: \(imageSize.width))")
            print("   Y: \(minY) ~ \(maxY) (图片高度: \(imageSize.height))")
        }

        // 创建路径遮罩，保持原图尺寸
        guard let maskedImage = createMaskedImageWithOriginalSize(image: image, pathPoints: absolutePathPoints) else {
            return image
        }

        // 新增：根据圈选边界裁剪图像，移除多余空白区域，使预览大小与圈选时一致
        if let croppedToBounds = cropImage(maskedImage, toPathPoints: absolutePathPoints) {
            print("✅ 根据圈选边界裁剪成功，最终尺寸: \(croppedToBounds.size)")
            return croppedToBounds
        }

        return maskedImage
    }

    /// 根据路径点裁剪图像到最小边界矩形
    private func cropImage(_ image: UIImage, toPathPoints pathPoints: [CGPoint]) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        guard let minX = pathPoints.map({ $0.x }).min(),
              let maxX = pathPoints.map({ $0.x }).max(),
              let minY = pathPoints.map({ $0.y }).min(),
              let maxY = pathPoints.map({ $0.y }).max() else {
            return nil
        }

        // 计算裁剪矩形并确保在图像范围内
        let imageWidth = CGFloat(cgImage.width)
        let imageHeight = CGFloat(cgImage.height)

        // 添加边距以保持边缘的平滑性
        let padding: CGFloat = 10.0  // 增加边距，确保平滑边缘不会被裁剪掉
        
        let originX = max(0, floor(minX) - padding)
        let originY = max(0, floor(minY) - padding)
        let width = min(imageWidth - originX, ceil(maxX) - floor(minX) + padding * 2)
        let height = min(imageHeight - originY, ceil(maxY) - floor(minY) + padding * 2)

        // 防止异常尺寸
        guard width > 1, height > 1 else { return nil }

        let cropRect = CGRect(x: originX, y: originY, width: width, height: height)
        print("📐 裁剪到边界矩形(带边距): \(cropRect)")

        guard let croppedCG = cgImage.cropping(to: cropRect) else { return nil }
        return UIImage(cgImage: croppedCG)
    }

    /// Create masked image using path points while preserving original size
    private func createMaskedImageWithOriginalSize(image: UIImage, pathPoints: [CGPoint]) -> UIImage? {
        guard !pathPoints.isEmpty else {
            print("⚠️ 圈选路径为空，返回原图")
            return image
        }

        print("🎨 开始创建圈选遮罩（保持原尺寸），路径点数: \(pathPoints.count)")
        print("📐 图片尺寸: \(image.size)")

        let imageSize = image.size
        guard let cgImage = image.cgImage else {
            print("❌ 无法获取CGImage")
            return image
        }

        // 创建透明背景的图形上下文，保持原图尺寸
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        guard let context = CGContext(
            data: nil,
            width: Int(imageSize.width),
            height: Int(imageSize.height),
            bitsPerComponent: 8,
            bytesPerRow: 0,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            print("❌ 无法创建透明图形上下文")
            return image
        }

        // 修正坐标系：Core Graphics的Y轴与屏幕坐标系相反，需要翻转Y坐标
        let flippedPathPoints = pathPoints.map { point in
            CGPoint(x: point.x, y: imageSize.height - point.y)
        }

        print("🔄 坐标系翻转: \(pathPoints.count)个点")

        // 创建平滑的贝塞尔曲线路径，而不是直接连接点
        let path = CGMutablePath()
        guard flippedPathPoints.count > 0 else {
            print("❌ 翻转后路径点为空")
            return image
        }

        // 先简化路径点（减少计算量，但保留更多细节）
        let simplifiedPoints = simplifyPath(flippedPathPoints, tolerance: 3.0)
        print("📊 简化后路径点数量: \(simplifiedPoints.count)")
        
        // 创建平滑的贝塞尔曲线路径
        path.move(to: simplifiedPoints[0])
        
        if simplifiedPoints.count > 2 {
            // 使用三次贝塞尔曲线连接点，增强平滑度
            for i in 0..<simplifiedPoints.count {
                let current = simplifiedPoints[i]
                let next = simplifiedPoints[(i + 1) % simplifiedPoints.count]
                
                // 计算控制点（使用前后点计算切线）
                let prev = simplifiedPoints[(i + simplifiedPoints.count - 1) % simplifiedPoints.count]
                
                // 计算切线向量
                let tangent1 = CGPoint(
                    x: (next.x - prev.x) * 0.3,  // 使用0.3作为张力系数
                    y: (next.y - prev.y) * 0.3
                )
                
                let tangent2 = CGPoint(
                    x: (simplifiedPoints[(i + 2) % simplifiedPoints.count].x - current.x) * 0.3,
                    y: (simplifiedPoints[(i + 2) % simplifiedPoints.count].y - current.y) * 0.3
                )
                
                // 计算控制点
                let control1 = CGPoint(
                    x: current.x + tangent1.x,
                    y: current.y + tangent1.y
                )
                
                let control2 = CGPoint(
                    x: next.x - tangent2.x,
                    y: next.y - tangent2.y
                )
                
                // 添加三次贝塞尔曲线
                path.addCurve(to: next, control1: control1, control2: control2)
            }
        } else {
            // 点数太少，直接连线
            for i in 1..<simplifiedPoints.count {
                path.addLine(to: simplifiedPoints[i])
            }
        }
        
        // 封闭路径
        path.closeSubpath()

        // 检查路径是否有效
        let pathBounds = path.boundingBox
        print("📏 平滑后路径边界: \(pathBounds)")

        if pathBounds.isEmpty || pathBounds.width < 1 || pathBounds.height < 1 {
            print("⚠️ 路径边界无效，返回原图")
            return image
        }

        // 设置裁剪路径并绘制图片
        context.addPath(path)
        context.clip()

        // 绘制原始图片，保持原始尺寸和位置
        context.draw(cgImage, in: CGRect(origin: .zero, size: imageSize))

        // 获取裁剪后的图片
        guard let resultCGImage = context.makeImage() else {
            print("❌ 圈选遮罩创建失败")
            return image
        }

        let result = UIImage(cgImage: resultCGImage)
        print("✅ 圈选遮罩创建成功，结果尺寸: \(result.size)")
        return result
    }
    
    /// 简化路径点（Douglas-Peucker算法的简化版本）
    private func simplifyPath(_ points: [CGPoint], tolerance: Double) -> [CGPoint] {
        guard points.count > 2 else { return points }

        var simplified: [CGPoint] = [points[0]]

        for i in 1..<points.count-1 {
            let current = points[i]
            let last = simplified.last!

            // 计算距离，如果距离大于阈值则保留点
            let distance = sqrt(pow(current.x - last.x, 2) + pow(current.y - last.y, 2))
            if distance > tolerance {
                simplified.append(current)
            }
        }

        // 总是保留最后一个点
        simplified.append(points.last!)

        return simplified
    }

    /// Create masked image using path points (legacy method)
    private func createMaskedImage(image: UIImage, pathPoints: [CGPoint]) -> UIImage? {
        guard !pathPoints.isEmpty else { 
            print("⚠️ 圈选路径为空，返回原图")
            return image 
        }
        
        print("🎨 开始创建圈选遮罩，路径点数: \(pathPoints.count)")
        print("📐 图片尺寸: \(image.size)")

        let imageSize = image.size
        guard let cgImage = image.cgImage else {
            print("❌ 无法获取CGImage")
            return image
        }

        // 创建透明背景的图形上下文
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        guard let context = CGContext(
            data: nil,
            width: Int(imageSize.width),
            height: Int(imageSize.height),
            bitsPerComponent: 8,
            bytesPerRow: 0,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            print("❌ 无法创建透明图形上下文")
            return image
        }

        // 创建路径
        let path = CGMutablePath()
        path.move(to: pathPoints[0])
        for i in 1..<pathPoints.count {
            path.addLine(to: pathPoints[i])
        }
        path.closeSubpath()
        
        // 检查路径是否有效
        let pathBounds = path.boundingBox
        print("📏 路径边界: \(pathBounds)")
        
        if pathBounds.isEmpty || pathBounds.width < 1 || pathBounds.height < 1 {
            print("⚠️ 路径边界无效，返回原图")
            return image
        }

        // 设置裁剪路径并绘制图片
        context.addPath(path)
        context.clip()
        
        // 绘制原始图片，只有路径内的区域会被绘制，其他区域保持透明
        context.draw(cgImage, in: CGRect(origin: .zero, size: imageSize))

        // 获取裁剪后的图片
        guard let resultCGImage = context.makeImage() else {
            print("❌ 圈选遮罩创建失败")
            return image
        }
        
        let result = UIImage(cgImage: resultCGImage)
        print("✅ 圈选遮罩创建成功，结果尺寸: \(result.size)")
        return result
    }

    /// Get custom trigger display color for image
    func getCustomTriggerColor(for imageName: String) -> Color {
        let config = getCustomTriggerDisplay(for: imageName)
        
        // Handle rainbow color mode
        if config.displayColor == "rainbow" {
            return getRainbowColor()
        }
        
        // Handle multi-color mode
        let selectedColors = dataService.loadSelectedColors(for: imageName)
        if selectedColors.count > 1 {
            return getRotatingColor(for: imageName, from: selectedColors)
        }
        
        // Single color mode
        return AppTheme.getColor(fromName: config.displayColor)
    }
    
    /// Trigger image interaction
    func triggerImage(for imageName: String,
                     imageManager: ImageManager,
                     soundManager: SoundManager) {
        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        // Increment trigger count (always +1, regardless of increment value)
        var settings = imageManager.getImageSettings(for: imageName, in: modeContext)
        settings.clickCount += 1
        imageManager.updateImageSettings(for: imageName, in: modeContext, settings: settings)
        print("🎵 triggerImage - imageName: \(imageName), 使用上下文: \(modeContext)")

        // 多图片模式：为当前显示的图片播放音效
        let targetImageName: String
        if settings.isMultiImageMode {
            targetImageName = settings.currentDisplayImageName
        } else {
            targetImageName = imageName
        }

        // 播放音效
        playImageSound(for: targetImageName, soundManager: soundManager, imageManager: imageManager)
    }

    /// Trigger image interaction with custom increment value (for random hints)
    func triggerImageWithCustomIncrement(for imageName: String,
                                       incrementValue: Int,
                                       imageManager: ImageManager,
                                       soundManager: SoundManager) {
        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        // Increment trigger count (always +1, regardless of increment value)
        var settings = imageManager.getImageSettings(for: imageName, in: modeContext)
        settings.clickCount += 1
        imageManager.updateImageSettings(for: imageName, in: modeContext, settings: settings)
        print("🎵 triggerImageWithCustomIncrement - imageName: \(imageName), 使用上下文: \(modeContext)")

        // 多图片模式：为当前显示的图片播放音效
        let targetImageName: String
        if settings.isMultiImageMode {
            targetImageName = settings.currentDisplayImageName
        } else {
            targetImageName = imageName
        }

        // 播放音效
        playImageSound(for: targetImageName, soundManager: soundManager, imageManager: imageManager)
    }

    /// 播放图片关联的音效（提取的公共方法）
    private func playImageSound(for imageName: String, soundManager: SoundManager, imageManager: ImageManager) {
        // Play associated sounds using image-specific configurations
        let names = soundManager.getSoundNames(for: imageName)
        print("🎵 TriggerManager.playImageSound - imageName: \(imageName), 音效列表: \(names)")
        if !names.isEmpty {
            soundManager.playMultiSounds(names: names, for: imageName, imageManager: imageManager)
        } else {
            print("🎵 没有找到音效配置: \(imageName)")
        }
    }
    
    /// Get current trigger count for image
    public func getCurrentTriggerCount(for imageName: String, imageManager: ImageManager) -> Int {
        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        return imageManager.getImageSettings(for: imageName, in: modeContext).clickCount
    }

    /// Reset trigger count for image
    public func resetTriggerCount(for imageName: String, imageManager: ImageManager) {
        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        var settings = imageManager.getImageSettings(for: imageName, in: modeContext)
        settings.clickCount = 0
        imageManager.updateImageSettings(for: imageName, in: modeContext, settings: settings)
        
        // Reset color index
        resetColorIndex(for: imageName)
    }
    
    /// Get trigger mode for image
    public func getTriggerMode(for imageName: String, imageManager: ImageManager) -> ImageTriggerMode {
        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        return imageManager.getImageSettings(for: imageName, in: modeContext).triggerMode
    }
    
    /// Set trigger mode for image
    public func setTriggerMode(for imageName: String, mode: ImageTriggerMode, imageManager: ImageManager) {
        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        var settings = imageManager.getImageSettings(for: imageName, in: modeContext)
        settings.triggerMode = mode
        imageManager.updateImageSettings(for: imageName, in: modeContext, settings: settings)
    }
    
    /// Check if click count should be shown for image
    public func shouldShowClickCount(for imageName: String, imageManager: ImageManager) -> Bool {
        // 确定正确的上下文
        let modeContext: ModeContext
        if imageName.contains("_copy_") {
            modeContext = ModeContext(modeId: imageName)
        } else {
            modeContext = imageManager.getCurrentModeContext()
        }

        return imageManager.getImageSettings(for: imageName, in: modeContext).showClickCount
    }
    
    /// Set whether to show click count for image
    public func setShowClickCount(for imageName: String, show: Bool, imageManager: ImageManager) {
        var settings = imageManager.getImageSettings(for: imageName)
        settings.showClickCount = show
        imageManager.updateImageSettings(for: imageName, settings: settings)
    }
    
    /// Get animation style for trigger display
    func getAnimationStyle(for imageName: String) -> TriggerAnimationStyle {
        return getCustomTriggerDisplay(for: imageName).getCurrentAnimationStyle()
    }
    
    /// Get font size for trigger display
    func getFontSize(for imageName: String) -> Double {
        return getCustomTriggerDisplay(for: imageName).fontSize
    }
    
    /// Get emoji for trigger display
    func getEmoji(for imageName: String) -> String {
        return getCustomTriggerDisplay(for: imageName).emoji
    }
    
    /// Check if increment should be shown in trigger display
    func shouldShowIncrement(for imageName: String) -> Bool {
        return getCustomTriggerDisplay(for: imageName).showIncrement
    }
    
    /// Save selected colors for image
    func saveSelectedColors(_ colors: Set<String>, for imageName: String) {
        dataService.saveSelectedColors(colors, for: imageName)

        // Update display color if single color is selected
        if colors.count == 1, let color = colors.first {
            var config = getCustomTriggerDisplay(for: imageName)
            config.displayColor = color
            setCustomTriggerDisplay(for: imageName, config: config)
        }
    }

    /// Generate random trigger display configuration
    func generateRandomTriggerDisplay() -> CustomTriggerDisplay {
        var config = CustomTriggerDisplay()

        // 随机提示始终使用文字模式，不依赖用户的自定义配置
        config.displayMode = .text

        // 随机选择自定义文本
        config.customText = AppConfig.randomTextPresets.randomElement() ?? "太棒了!"

        // 根据文案末尾是否为标点符号决定是否显示增量
        config.showIncrement = shouldShowIncrementForText(config.customText)

        // 随机选择增量值
        config.incrementValue = generateWeightedRandomIncrement()

        // 随机选择字体大小
        config.fontSize = Double.random(in: AppConfig.fontSizeRange)

        // 随机选择动画样式
        config.animationStyle = TriggerAnimationStyle.allCases.randomElement() ?? .bounce

        // 随机选择颜色
        config.displayColor = AppConfig.defaultColors.randomElement() ?? "white"

        print("🎲 生成随机配置: 文本=\(config.customText), 增量=\(config.showIncrement ? "+\(config.incrementValue)" : "无"), 字体=\(config.fontSize), 动画=\(config.animationStyle), 颜色=\(config.displayColor)")

        return config
    }

    // 判断是否应该显示增量（基于文案末尾字符）
    private func shouldShowIncrementForText(_ text: String) -> Bool {
        guard !text.isEmpty else { return Bool.random() }

        // 定义标点符号集合
        let punctuationMarks: Set<Character> = ["!", "。", "？", "?", "！", "…", "~", "～", ".", ",", "，", "；", ";", ":", "："]

        // 获取文案的最后一个字符
        let lastCharacter = text.last!

        // 如果末尾是标点符号，不显示增量
        if punctuationMarks.contains(lastCharacter) {
            return false
        }

        // 如果末尾不是标点符号，随机决定是否显示增量
        return Bool.random()
    }

    // 生成加权随机增量值（小数值概率更高，排除0）
    private func generateWeightedRandomIncrement() -> Int {
        var finalValue: Int

        repeat {
            // 使用指数分布来生成加权随机数
            let random = Double.random(in: 0...1)

            // 使用指数函数，让小数值有更高的概率
            let weightedRandom = pow(random, 3)

            // 映射到范围 [1, maxAbsValue]，避免生成0
            let maxAbsValue = Double(AppConfig.incrementValueRange.upperBound)
            let scaledValue = max(1, weightedRandom * maxAbsValue)

            // 随机决定正负
            let isPositive = Bool.random()
            finalValue = isPositive ? Int(scaledValue) : -Int(scaledValue)

            // 确保在范围内且不为0
            finalValue = max(AppConfig.incrementValueRange.lowerBound,
                           min(AppConfig.incrementValueRange.upperBound, finalValue))
        } while finalValue == 0

        return finalValue
    }
    
    /// Load selected colors for image
    func loadSelectedColors(for imageName: String) -> Set<String> {
        return dataService.loadSelectedColors(for: imageName)
    }
    
    /// 异步预处理Toast图片，避免点击时延时
    func preloadCustomDisplayImage(for imageName: String) {
        // 检查是否已经在预加载队列中
        guard !preloadingTasks.contains(imageName) else {
            print("🔄 图片 \(imageName) 已在预加载队列中，跳过")
            return
        }
        
        // 检查是否已经有缓存
        let config = getCustomTriggerDisplay(for: imageName)
        let cacheKey = generateToastImageCacheKey(for: imageName, config: config)
        if TriggerManager.toastImageCache.object(forKey: cacheKey as NSString) != nil {
            print("💾 图片 \(imageName) 已有缓存，跳过预加载")
            return
        }
        
        // 添加到预加载任务队列
        preloadingTasks.insert(imageName)
        print("📱 开始预加载Toast图片: \(imageName)")
        
        preloadQueue.async { [weak self] in
            _ = self?.getCustomDisplayImage(for: imageName)
            
            // 完成后从任务队列移除
            DispatchQueue.main.async {
                self?.preloadingTasks.remove(imageName)
                print("✅ 完成预加载Toast图片: \(imageName)")
            }
        }
    }
    
    /// 批量预处理多个图片的Toast版本
    func preloadCustomDisplayImages(for imageNames: [String]) {
        for (index, imageName) in imageNames.enumerated() {
            // 添加延迟避免同时启动太多任务
            preloadQueue.asyncAfter(deadline: .now() + Double(index) * 0.05) { [weak self] in
                self?.preloadCustomDisplayImage(for: imageName)
            }
        }
    }
    
    /// 清理Toast图片缓存
    static func clearToastImageCache() {
        toastImageCache.removeAllObjects()
        print("🗑️ Toast图片缓存已清理")
    }
    
    /// 获取不带缓存的Toast图片（调试用）
    func getCustomDisplayImageWithoutCache(for imageName: String) -> UIImage? {
        _ = getCustomTriggerDisplay(for: imageName)
        
        // 获取原始图片
        guard let originalImage = getOriginalImage(for: imageName) else {
            print("❌ 调试模式：无法获取原始图片: \(imageName)")
            return nil
        }
        
        // 直接返回压缩的原始图片，不进行裁剪
        let compressedImage = ImageCompressionUtils.compressForToast(originalImage) ?? originalImage
        print("🔍 调试模式：返回压缩原图，尺寸: \(compressedImage.size)")
        return compressedImage
    }
    
    // MARK: - Private Methods
    
    private func loadData() {
        customTriggerDisplays = dataService.loadCustomTriggerDisplays()
    }
    
    private func getRainbowColor() -> Color {
        let colors: [Color] = [.red, .orange, .yellow, .green, .blue, .purple, .pink]
        let index = Int(Date().timeIntervalSince1970) % colors.count
        return colors[index]
    }
    
    private func getRotatingColor(for imageName: String, from colors: Set<String>) -> Color {
        let colorArray = Array(colors).sorted() // Sort for consistency
        let index = getAndIncrementColorIndex(for: imageName) % colorArray.count
        return AppTheme.getColor(fromName: colorArray[index])
    }
    
    private func getAndIncrementColorIndex(for imageName: String) -> Int {
        let currentIndex = colorIndices[imageName] ?? 0
        let selectedColors = dataService.loadSelectedColors(for: imageName)
        let nextIndex = (currentIndex + 1) % max(1, selectedColors.count)
        
        colorIndices[imageName] = nextIndex
        
        // Persist to UserDefaults
        UserDefaults.standard.set(nextIndex, forKey: "colorIndex_\(imageName)")
        
        return currentIndex
    }
    
    private func resetColorIndex(for imageName: String) {
        colorIndices[imageName] = 0
        UserDefaults.standard.set(0, forKey: "colorIndex_\(imageName)")
    }

    /// Reset sound settings for an image to default
    public func resetSoundSettings(for imageName: String) {
        // 注意：音效配置实际上存储在ImageManager中，这里只是一个占位方法
        // 实际的重置逻辑在ImageSettingsView的重置按钮中处理
        print("已重置图片 \(imageName) 的音效设置")
    }

    /// Reset backtrack settings for an image to default
    public func resetBacktrackSettings(for imageName: String) {
        // 注意：回溯配置实际上存储在ImageManager中，这里只是一个占位方法
        // 实际的重置逻辑在ImageSettingsView的重置按钮中处理
        print("已重置图片 \(imageName) 的回溯设置")
    }

    /// Remove all trigger settings for an image when the image is deleted
    public func removeTriggerSettings(for imageName: String) {
        // 删除自定义触发器显示设置
        customTriggerDisplays.removeValue(forKey: imageName)
        
        // 删除颜色索引
        colorIndices.removeValue(forKey: imageName)
        
        // 删除UserDefaults中的相关数据
        UserDefaults.standard.removeObject(forKey: "colorIndex_\(imageName)")
        UserDefaults.standard.removeObject(forKey: "selectedColors_\(imageName)")
        
        // 删除DataService中的相关数据
        dataService.removeData(forKey: "\(AppConfig.UserDefaultsKeys.customTriggerDisplays)_\(imageName)")
        
        print("已删除图片 \(imageName) 的所有触发器设置")
    }
} 
