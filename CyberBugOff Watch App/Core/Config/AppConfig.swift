import Foundation

// MARK: - Global Configuration
struct AppConfig {
    // MARK: - Sound Configuration
    /// Default increment value for testing purposes
    static let defaultIncrementValue: Int = 1
    
    /// Maximum length for sound names
    static let maxSoundNameLength = 20
    
    /// Default sound volume
    static let defaultSoundVolume: Double = 1.0
    
    /// Default playback rate
    static let defaultPlaybackRate: Double = 1.0
    
    /// Sound playback rate range
    static let playbackRateRange: ClosedRange<Double> = 0.5...2.0
    
    /// Volume range
    static let volumeRange: ClosedRange<Double> = 0.0...1.0
    
    // MARK: - Image Configuration
    /// Default images bundled with the app
    static let defaultImages: [String] = ["bug1", "bug2", "bug3", "bug4", "bug5"]
    
    /// Default sounds bundled with the app
    static let defaultSounds: [String] = ["sound1", "sound2", "sound3", "sound4"]
    
    /// Image cropped suffix identifier
    static let croppedImageSuffix = "_cropped_"
    
    // MARK: - UI Configuration
    /// Default font size for custom trigger displays
    static let defaultFontSize: Double = 20.0
    
    /// Font size range for custom trigger displays
    static let fontSizeRange: ClosedRange<Double> = 12.0...36.0
    
    /// Default colors for UI elements
    static let defaultColors: [String] = [
        "white", "red", "orange", "yellow", "green",
        "blue", "purple", "pink", "gray", "black"
    ]

    /// Random configuration presets for custom trigger display
    static let randomTextPresets: [String] = [
        "🧘‍♀️静心ing","咚～悟了","🐢别急","敲你心门","咚咚咚咚咚","功德MAX✨","⛩️平安喜乐","佛说YES","悟了个寂寞","😌一念放下","锦鲤上身🫧","🌈转运啦","咕噜咕噜🐠","欧气+爆","开运小叮咚🔔","幸运BUFF","💰来钱了？","财神敲门","福到没道理","好运叮咚~","美爆啦💥","颜值暴击⚡","美貌当饭吃","🪞镜子狂喜","你好香🍓","发光体✨","漂亮到爆","仙女锤💫","哇，绝了！","灵光一闪✨","头好痛🌀","脑袋发光","🧠在线升级","知识暴增","读心了…","一念通神","学霸气场","🤓秒懂了","钱来啦💸","发发发📈","摸金锤🪙","工资到账","钞能力⚡","脱贫成功！","💎我要暴富","财神保佑","卡余额++","脑袋值钱了？","咚你咧！","你敲我干嘛","啊咧？","怎么还敲","真上头🤯","手痒警告⚠️","咚得妙~","你很会敲欸","敲敲小怪兽👾","咚咚变形！","🐾软乎乎","咚咚哒～","锤你心口❤️","喵喵功德","敲出个小可爱","咚咚龙龙🔮","(´•ω•`)咚","咚你一下罢了","🐰不生气啦","啾咪一下💋","呼~放松了","捶走烦恼","情绪++","🧃解压中…","嗯，好像好点了","平复一下","内心咚咚","让它过去吧","心锤师上线","叮咚平和了","咚你没商量","啥也没敲到","继续咚！","咚出内卷😵‍💫","抽象行为","咚咚显灵！","脑袋嗡了","你真会敲欸","敲爆现实🪐","敲一下清醒了","桃花预警🌸","心动咚咚❤️","爱来啦！","恋爱感满格","约会好运++","敲出红线💘","单身退散⚔️","你今天好香","💌好感上升","笑得像在恋爱","升！升！升！","工资++💰","假装月入过万","咚出个金饭碗","老板给涨薪🪙","富得咚咚响","卡上见💳","咚你发财梦","变现成功👏","财富代码启动！","🧂别emo了","咚清醒剂💊","打工人续命","脑袋修复中","啥都别想了","骂醒自己⚡","别再内耗了","清除脑内缓存","咚咚戒焦虑","放下执念吧～","神明在线🌙","运气爆棚啦","咚咚许愿中","好运开挂🔮","八字匹配中","灵咚咚出现","福气到账📦","开运香上线","摸了佛头👀","上香成功🕯️","慢一点点🍃","心情被敲好","今天不emo了","云朵感🫧","咚出勇气值","被温柔锤醒","情绪软着陆","抱抱你🧸","你已经够好了","晒太阳吧🌞","咚咚咚咚咚","哈？啥敲了？","嗨，锤子人","已空格…","脑袋当场离线","这不是木鱼？","咚你个鬼啦","灵魂已上传","咚咚咚穿越了","锤爆元宇宙","我咚你了耶~","咚咚猪猪锤🐷","小猫被敲醒🐱","给你咚个赞✨","咚咚兔兔敲🐰","你是个宝～💎","锤你心头肉💓","脑袋蹦小花🌼","喵喵也敲！","哇哦你最乖～","咚~","悟了","啊咧？","静心","别急","发财","欧气","叮咚","上头","绝了","美爆","仙女","升级","秒懂","发了","暴富","喵喵","好香","呀！","来啦","吓我","好运","转运","欧了","妙哉","碎啦","亮了","清醒","飘了","心动","走你","咕噜","破防","抱抱","发光","快跑","妙咚","哇哦","嘻嘻","呜呜","开挂","暴击","福到","乱敲","稳住","暴涨","可怕","别敲","锤爆","醒醒","续命","脱单","有谱","要发","磕了","嗨咚","认锤","上香","叮了","来钱","妙了","你乖","忍住","别内耗","再敲","别emo","咚你","真香","有了","稳啦","没事","宝～","呼~","靓仔","上热搜","嗑疯了","锤你","捶了","真上头","已空格","好点了","发财梦","别再敲","别emo了","你很会","功德++","脑袋嗡","太疯了","你最美","心锤师","你在线","好运++","财神到","咚咚哒","颜值++","别冲动","好运咚咚","财神敲门","脑袋发光","金饭碗咚","好运开挂","发疯叮咚","叮咚～","发财啦","来了啊","功德咚","在线咚","敲敲敲","锤你了","啊这…","美晕了","要啥来啥","心很静","忍一忍","好运来","没了欸","妙咚咚","钞能力","有点晕","锤碎了","神显灵","速速退","清净点","先别敲","停一停","我在听","灵气涨","暴瘦了","颜值飙升","值钱了","神锤手","别烦我","醒啦～","敲神经","修仙啦","灵体动","福气在","你挺美","继续敲","快发财","接好运","别emo啦","锤福星","别气啦","来灵了","你敲啥","锤美貌","睡不着","你打我？","脑袋嗨","要疯啦","算我输","哎哟喂","升职啦","抱住你","别生气","醒神啦","想开点","真好运","好运到","财神嗨","敲中你","我太美","哇好看","崩掉啦","治好我","别再内耗","锤醒你","你真行","美丽叮咚","别闹啦","今天赢","我不累","打工魂","别发呆","快许愿","神明保佑","咚你好运","太上头","敲晕我","祝福你","敲福气","在线玄学","嘿嘿嘿","敲掉穷","想暴富","冲业绩","真香警告","我裂开","功德在冒","功德火箭🚀","感情顺利","头发保住","敲桃花","咚翻全场","咚爆内耗","真解压","锤爆烂事","卡bug了","念力加持","功德还行","好运显现","财神降临","运气咚咚","再敲封号","神秘力量","灵感咚来","心灵spa","哇偶~","气场++","好运等你","别卷啦","脑袋断片","成功打工人","你是锦鲤","神力输出","财富涌入","气质拿捏","你不一样","佛门鼓掌","在线灵修","咚咚吃瓜","思路清晰","哇，好帅","灵魂共振","你清醒点","暴富警告⚠️","好运快递📦","功德奶茶🥤","秒杀焦虑","锤向富贵","你好仙","月光保佑🌙","星运++","梦实现啦","暴击烦恼","咚了烦恼","咚穿宇宙","灵咚咚动","呼吸好运","打工清醒剂","你好乖","许愿成功","功德满仓","敲出副本","真会敲","亿点好运","帅疯了","闪现好运✨","超神啦","暴富冲鸭","灵光乍现","太灵啦","快变富婆","福气附体","祝你好梦🌙","快下凡吧","你在发光","好运甜甜圈🍩","摸鱼许可🐟","嘿你真美","敲中要害","好运预载中","星光附体✨",
        "🧘静住了","💰来钱了","🧠充电中","🍀转运啦","✨灵光现","🌀开转了","🎉爆喜感","📈起飞咯","💫啥都好","🧿保平安","📿佛系住","🔮摸鱼了","🔥又燃了","👻溜了哈","🥹有点感","🤡谁懂啊","🎁好运开","🐟咕噜噜","🐱好乖哦","🐶给我冲","🌈彩虹现","💤睡着了","🌙平安夜","🧸抱一下","🍉爽翻天","🌸花开啦","🍓甜住了","🪙小钱钱","💎真贵气","🫧轻轻叹","🍩好吃欸","🥳有喜气","🫠化了我","📡接好运","💭想什么","🛐叩拜中","🧃多喝水","🍒哎呦喂","🎯击中我","🤯爆头了","📸拍一张","🪄魔法哒","💋啵一口","🪞好美诶","🛸飞起来","🫶爱你呦","🤓很会欸","💥炸了耶","🦄去飞吧","🐾跟上啦","📦打包了","🧠转念了","🥰感觉棒","🙈没看到","🤲全给你","💡灵感咻","📀别转了","🪬驱邪中","🧿护体啦","🐲开大啦","🌊脑子进","📱震一下","📎贴贴呀","🧩拼好了","🌌宇宙说","📈向上冲","🥴晕了哦","🎲决定吧","🧬变强了","🛕佛保佑","🦋飞一飞","🍪咔咔吃","🧷扣住啦","🧼洗净了","💉打满了","🍭超可口","🎀绑住你","🍃飘起来","🧺装好运","🌽玉米人","🥺求一下","😌太行了","😳谁懂啊","🫥消失中","😮‍💨吐口气","🌵扎一下","🕯️点上啦","💡想到了","🧳去旅行","🧠胡思乱","📱点到啦","🧘呼——哈","🐰萌混过","🐼蹲一个","🐤啾一下","🦊悄悄来","🐘重一点","🦖啪一声","🐚听海浪","🪴开花了","🪷福报开","🛕响一下","🐝嗡嗡响","🪹空的欸","🦚飘起来","🕊️走一趟","🔔响一下","🪐转星运","🪦平平安","🌐全靠它","📡连接中","📞在线啦","🌙睡一下","🧿擦好运","🧠乱转啦","🎧嗨起来","🧱砌好啦","💼搞钱中","🎮打一把","🍄梦开始","🌾扎心了","🌌星星闪","🧿啪叽响","💋啾一下","🐵闹起来","🌟星运动","🧿再来个","📿扎功德","🎲抛骰子","🐼萌住了","🫰爱心发","🪄魔法光","🧠整顿中","📈涨一涨","💸稳稳哒","🛐合掌了","📸哇靠拍","🐚运来了","💻智慧包","🪄魔动哒","🛸飞行中","🐾小脚丫","🎀缠上你","🪞美得咧","🎯射好运","🧃补水咯","🍰开吃咯","🐻熊来啦","🎁开个包","🧿👀保你","📿🛐祈福","🌈🍀变好运","💸💎赚爆了","🧠✨灵动了","🔮📡接能量","🐟📈翻红啦","🍓🧁吃好甜","💋🫦发电中","🪐💫转运喽","🧘🧠稳心神","🍀好运冲！","冲了！💸","保佑我🙏","别想啦","🤲全给你","赚疯了😵‍💫","🌈要发啦","等等我🫠","🧠又晕了","上香了🛐","开摆🧘‍♀️","💰收钱中","有点爱😳","💫真神奇","🌊冲走了","🐶快跑叭","🍉太甜啦","哦豁😅","咕咚一下🥴","😮‍💨怎么啦","🐚平安着","帮我转运✨","📈冲高咯","福到了🎁","别吓我😵","🧃补补脑","想摸鱼了🐟","🌀一整套","来点玄的🔮","🧿挡一下","抱一个🧸","叠加好运📿","哎哟不错🤓","🪙小钱钱进","累了😮‍💨","🍓果然甜","再试一次🎲","哇哦🥹","发啦📈📈","再来！","🐾走一步","🌙求安稳","稳了！🧘","佛保佑🛐","打工人上线💼","碎银几两🪙","想太多啦🤯","亲亲💋","抱一抱🐼","哎嘿嘿🤭","又炸了💥","啊？😳","😇真不错","🧠想啥呢","😶‍🌫️虚掉了","别急🐢","来啦来啦🫡","🫧泡泡心情","睡一觉😴","🧘‍♂️气归位","咻～✨","🎯准不准","我裂开了💔","好巧🪄","🍿继续看","叮！🔔","下饭了🍛","嘤嘤嘤🐣","❄️冷静下","再等等🫠","点到我了🖱️","好好玩🎮","🎵听上头了","稳住我们能赢✊","🥹感动咯","我太棒啦🌟","🪷功德+1","一言不合💨","😭我不管","🫨抖三抖","🧿求一签","喝水记得🧃","再点试试🔁","又是你🫵","🌶️辣到我","小心心发射🫰","就这？🙃","🕊️放飞啦","📦打包好运","✨闪现啦","刷一下🧹","🪐星辰指引","我哭死😭","😎帅一波","好涩🌶️","想发疯🤪","贴贴📎","摇人了📞","也太准了🔮","🧠清醒中","打咯🥊","⛩️保命中","开香槟啦🍾","有点慌🥺","📶信号来了","🚨注意！","🧸别闹啦","啥都来点🥴","😶虚了个虚","睡饱再说😴","🔁循环中","全靠玄学🔮","🤔认真了","发财发财💰","🌞开朗咯","上头了🎧","还来啊😮‍💨","都行吧🙃","🌻来点阳光","🌋爆了爆了","🧃喝口水","我不信了😤","躺平中🛏️","👁️看透了","再转一下🎲","冲鸭！🦆","🍂凉凉哒","都别说话🤫","快说快说🗣️","📡能量同步","你说得对😶","🎯再中一个","🐸呱呱呱","🍵品一口","🧿挡挡挡","🌞运气上头","猜错啦😶‍🌫️","💤没醒呢","我裂了🫠","🫡收到咯","给我康康👀","来点大的💥","稳如老狗🐕","😵‍💫转晕咯","原地结婚💒","🤯脑袋炸","不能再点了🚫","👻吓一跳","😝好会哦","🪬稳住我","给我好运🍀","我想一下🤔","🧙‍♂️预言一下","拉了拉了🪤","不说了🫠","已转运✨","试试看🔂","进了吧📦","🧽擦干净","贴脸赢📍","缓缓冲🧘","🧂加点咸","🍵茶来咯","🐍蛇住了","🧶扯线中","想发财💸","这不准吧？🧐","🌈又来了","碎了碎了😢","🧠啊啊啊","唉～","嘟嘟🚨","还行！","真·好耶🥹","🧃再一杯","😅社死咯","🙏许个愿","撑一下💪","🛐人间清醒","📿点功德","吃瓜了🍉","冲到尽头🏁","👀盯着你","🫥消失吧","🍂又一秋","你说呢🙄","不做了🫠","收工吧💼","放空中🛏️","🧠系统宕机","再赌一次🎲","睡不醒😴","好准好准🔮","我裂开🧠","来个狠的🔥","📢大喊一下","摸到福啦🪬","有点神欸🧿","🧨一声炸","🌸香到我",
        "冲鸭！🦆","别摸我啦😳","钱包鼓起来💰","请叫我大神✨","幸福来敲门🙏","动力满满哒💪","咕咕咕🐔","快点夸我嘛🥹","稳稳冲上去📈","元气爆棚了🔥","萌萌哒上线🐰","美丽不打烊💄","好运气蹦出来🌈","嘻嘻哈哈哈😂","发发发发发💸","加油鸭加油🦆","不慌，稳住😌","钞票滚滚来🪙","心想事成哦🍀","爆灯！开挂了💡","运气好到飞🕊️","甜甜的蜜糖🍓","快乐不停歇😄","财源广进啦💰","小心别闪到⚡","快乐每一天🌞","发芽的感觉🌱","美梦马上到✨","幸福满满哒🥰","元气满满哦🔥","好事马上来🎉","笑出腹肌啦🤣","别急别急嘛⏳","别摸，别摸🙅‍♂️","好运连连看🍀","发发发冲鸭🦆","钞票多多来💵","转运转运啦🔮","冲鸭冲鸭🦆","乐开花咯🌸","大吉大利呀🍀","甜甜圈来啦🍩","花开富贵🌷","再接再厉哦💪","福星高照啦🌟","小幸运降临🍀","冲上云霄啦🚀","走好运哦👍","笑口常开哦😁","别急别急⏳","好运蹭蹭涨📈","能量满满哒⚡","小心别闪到⚡","闪闪惹人爱✨","好运气来了🎉","给力给力👍","我要发光啦💡","开心每秒钟😊","好运冲冲冲🔥","多福多寿🍀","乐呵呵的😄","别慌别慌😌","稳住别浪🌊","冲破天际🌌","宝藏发现啦🔑","爱你哟🫶","财运亨通啦💰","美美哒今天💄","逆风翻盘⚡","好运成双🍀","幸运降临🦄","继续冲鸭🦆","大吉大利💰","炸裂开花💥","我裂开了😭","开心果来了🥳","好运翻倍啦📈","元气满满⚡","发光发热✨","甜甜的味道🍬","稳稳幸福感❤️","阳光正好☀️","被爱包围了💕","收获满满🎁","每天都开心😊","给力有木有💪","别走宝了🏃‍♂️","好运蹭蹭涨🔥","冲上云霄🚀","开心到飞起🕊️","一切皆有可能🌟","甜蜜爆表🍰","全力以赴⚡","未来可期🌈","乘风破浪🌊","快乐出发啦🚀","运气爆棚了🎉","好运挡不住🍀","发大财了💸","继续发光✨","别想太多🧠","笑出腹肌🤣","全力加油💪","惊喜连连🎁","好运在招手👋","招财进宝💰","要好运哦🍀","全程高能⚡","幸福不打烊❤️","开心爆棚了🥳","多福多寿来🍀","稳稳的幸福感😊","爱你哟🫶","继续冲鸭🦆","发光小可爱✨","叮咚好运来🔔","笑口常开😁","好运随行🍀","福气满满啦🎉","动力满满哒💪",
        "🧘","✨","💸","🐟","🧠","🧡","🌈","🍀","😵","🥹","🙈","🫠","🥰","👻","😳","🎉","🍉","😌","😺","🐰","🌊","🌟","🫧","🌀","💫","⚡","🔥","🎯","🎁","💎","🥳","🤩","🫶","🤭","🤓","💤","🤯","😮‍💨","🥴","💥","🫨","🫃","🙇","🕯️","🌙","🧃","📿","🪄","🍩","🔮","📈","🚀","🎲","🎮","🍄","🧸","🪙","🪷","🧿","💡","🌸","🍭","💐","💋","🛐","🥺","👀","🤡","💭","🤲","🧺","🎀","🫰","🐶","🐱","🐼","🐤","🍒","🍪","🌽","🍓","🍰","🪞","🫥","🫂","🛸","🗿","🐲","🐉","👽","🤖","📦","💼","🧳","🧱","💻","📱","⏰","🔔","🔋","🔑","🧷","📎","🧩","💳","📀","🧼","💊","💉","🧬","🎧","📸","📡","🌐","🪐","🌌","⛩️","🕌","🕋","🪦","🛕","🐚","🍃","🪹","🌳","🌵","🪴","🍂","🌾","🐾","🦋","🐝","🦄","🦊","🐻","🐮","🐷","🐸","🐵","🦕","🦖","🐘","🐫","🦩","🪶","🦚","🕊️","🐾🐾","🐟✨","💰💰","🧘✨","🎉🎉","📿🛐","🌙🕯️","💡🧠","🔮🧿","🧸🍭","🧘🌀","🐉🔥","😵🌀","🧠⚡","📈📦","💸🪙","🌈🍀","🧠🧘","💫🪄","🧠📶","🎯✨","🎮🧠","💎💰","🌟📈","🐱🍡","🐶🍖","🍩🧃","🌸💐","🧿🔮","🧃💊","🛐📿","🫧🌀","🧘💫","✨🫠","🌙🌊","🪷💫","🧠💥","💤🧘","🪙📈","🧘📿","🧠🧿","💰📦","🧠📊","🍰🍓","🧠🧊","🧠🍵","🧠🎯","🧠🔧","🧠💾","🧠🧽","🧠🛠","🧠💤","💸🛐","💸🔮","💸📈","💸🪙","💸💎","💸📦","💸🎁","💸🎉","💸🪄","💸✨","💸🧿","💸🛸","💸🔋","💸🪬","🧘📿✨","🎉💸🎯","🧠💡🫧","📈💰🎯","🌈🍀📦","🎁📦💸","🔮🧿🛐","🧠🪄💡","💎📈💰","🫧🌀🌟","💰","🪙","🎉","🌠","🧿","📿","🔮","🛐","🙏","😇","🌸","🪷","🐟","🐚","🍀","🌈","🪄","✨","💫","🔆","🔥","⚡","💡","🧠","🤯","🧘","🪬","🎊","🎁","📦","💎","📈","📊","🆙","💼","📱","🧸","🧃","🍩","🍓","🎂","🍡","🪞","🧼","💄","🪮","🎀","👑","🧦","👛","👜","🩷","🩵","💚","💙","💛","🖤","🧡","💟","💘","💖","💗","💓","🫶","🤲","🤝","👏","👀","🤩","🥳","😎","🤭","🥺","🫣","🫠","🫥","🤤","🤪","😵‍💫","🫨","🫃","🤡","👻","👽","🧞","🧜","🧚","🦄","🐰","🐱","🐶","🐸","🐷","🦊","🦋","🐝","🕊️","🐾","🌻","🌼","🌷","🍁","🍃","🌿","🪴","🌵","🌳","🌲","🌀","💨","🌬️","🫧","🧊","🌊","🌫️","🌌","🌙","🌞","🌅","🌄","🪐","⭐","🌟","💥","💣","🎇","🎆","💫","💦","😮‍💨","🫗","🫱","🫲","🤌","👋","🤙","✌️","🖖","🤘","🫰","🙏✨","📿🧘","🔮🧿","💰📈","🧠⚡","🍀🌈","🐟✨","🎁📦","🛐🙏","📈💹","💎🪙","🌟🎉","🌸🪷","💗🫶","🎊🎀","🧸🍭","🧠💡","💖💘","🌈🫧","🧃🍩","🍓🍰","🐰🧸","🧿🔮","📱💻","💼📈","💄🪞","👑🎀","📦📈","💳💰","🎉🎊","🧘🧿","🛐🪷","🫧💎","📿✨","🌸🌼","🍀🪴","🍓🍒","🎂🍡","🌙🔮","✨🔮","💫💥","🔥⚡","🤯🧠","🧠💡","🧠🎯","📊🆙","💰🪙","📈📦","🎁📦","💎📈","🌈🍀","💫🛐","🧿📿","🐟🪄","🍩🧃","🌸🍰","🧘📿","💗🌸","🪬🛐","🧊🌀","🌙🪐","⭐🌌","🌟🌙","🐾🦄","🐱🐾","🐝🌼","🐰🍓","🦊🎀","🐷🍭","🐸🌿","🐚🌊","🧚✨","🧜🫧","🧞🌠","💡🎯","📀📡","🖖🫰","🤝🤲","🫶👏","🫠😵","😎🤙","👻🎉","🧠🧊","🎀🎊","🛸🌌","💥💫","📦🪄","📿🔮","📈💸","💸💰","📊📈","🔮🌙","🌈🧿","🛐🙏📿","💡🧠✨","🎉💎🪙","🧘🔮📿","💼📈💸","🫧🌊🌀","🪷🌸🌼","🍰🧁🍩","🧿🔮💫","🧘✨🧠","📦📈💰","💎💸📈","💡📊📈","🧘📿✨","📈💰💎","🔮📿🛐","🐰🎀🧸","🐟🍀🌈","🧿🔮✨","🪄🌈📦","💡🧠🎯","🐱🍓🍡","🎉🎊🎁","💎📦📈","💖💗💘","🍓🍰🧃","🧘🌙🕯️","🧿🛐📿","🌊🫧🌀","🌸🪷🌼","🍀🌈📈","🐾🐰🧸","🦄🌟✨","🧠💡⚡","📊📈💰","💼📱📦","💄🪞🎀","👑💍🎁","💳📈💸","🐝🌼🌻","🐷🍭🍰","🎂🍓🍒","🍩🧁🧃","🌙🔮🧿","🌟🌈🪄","🎯📈💥","📦📊🪙","🪬🧿📿","🧠🎯💥","🎀🎁🧸","🐸🍃🌱","🌵🪴🍀","🌈💫🌠","🍒🍓🍰","📀📡🧠","🌌🪐🌠","🐾🦄🎀","🐶🐾🧸","💄💋🎀","🧠🧊🌀","💡🧠📊","📈💸💥","🐟📿🧘","🪙💰📈","💰💸💳","🔮🧿🪬","🫧🌀💫","🍰🧁🍩","🌸🎀🧸","💖💓💘","💼📈📊","📱🧠💡","🍡🍓🍒","🧠📈💸","🌙📿🛐","🎀🧸💖","🫰💖🎀","👀😳💥","🧠🤯💢","💥🔥⚡","🎉🎁💰","🌸🪷🍃","💎💰📦","🍭🍒🧁","🌠🌌🪐","🎂🍰🍡","🐰🎀🍓","📿🛐🧘","🌊🫧🌸","🧿📿🔮","💼📱💻","💰💳📈","🌈🍀📦","🧠🧊🫧","🐝🌼🍯","🌿🍃🪴","🧘🧿📿","🐱🧸🎀","🍓🍰🧁","📈💰💼","🎁📦📊","🍩🍭🎂","🌸💐🎀","🌙🔮📿","🪬🧿🔮","💸💰🪙","🧠🪄🎯","🐶🦴🧸","🍒🍓🍩","🧃🍰🍭","🧠💡📈","🌟🎯📈","🎉🎊💥","🌈✨🎁","📦📊📈","🌊🧘🫧","🌼🌿🍃","🐾🐱🎀","🧠🧿📊","🐟📿🛐","🧁🍰🍒","🧠⚡💥","🎀💝💖","🐱🍡🍓","💳💰🪙","🧠🛐📿","🎉📦🎁","🍩🍒🍓","🌸🌈✨","🍀📈📊","🌙🪐🌠","🎯🎁📈","🐰🧸🍰","🐾🪄🧸","🧠🔮💡","🛐📿🧿","🎉💥💫","🌈🧿🪄","💖🎀🧸","🍓🍩🍰","🧁🍭🧃","🪴🍃🌱","🎊🎁💰","🐾🎀🧸","💎💰📊","🧠💥🧊","🫧💫🌈","🌊🧘🌸","🐝🍯🌼","🍡🍓🧁","🧠📈🎯","🐰🍒🧁","💖💘💓","🧠🧘💫","🧿🔮🛐","🌸🌼🌷","🧠🧊📈","🎀👛🧸","🧠💡🔮","🍰🧁🎂","🌈🎉✨","🎁📦💎","💡🎯📈","🧿🪬🔮","📱💻📦","🐱🐾🎀","🧠🧿📊","💸💰📈","🎂🍰🍡","🧘📿🔮","🍒🍓🧁","🐾🦄🎊","🎁🎀💖","🎉🎊🎂","🎁💎📦","📿🔮🪬","🌙🌌🌠","🌟🎁📈","🧠💡🎯","📦💰📈","💸📈📊","🐰🎀🍩","🍭🍒🍓","🧠🔮🧘","🎯🎁🎊","📿🛐🧘","🎉💎📦","🧠🧿🔮","🌊🫧🌌","🌈🪄📿","💰💎🎁","🎀🧸💐","🍓🍰🍒","🎉🎁💥","🎯📦📈","🧘🌙🪷","🌠🌌🪐","🧿🔮🪬","🧁🍓🍰","📦📊💰","🌸🧸🎀","🪴🌿🍀","🧘✨🧠","💎📈💰","🧠💡📦","🐾🎀🧁","🍩🧃🍰","🎁🎉📦","💡🧠🎁","🌈🌠🎉","🧿📿🔮","🎂🍒🍓","🌸💐🌼","🧠💡✨","📊📈🎯","🐰🍰🧁","🎁📦💰","💸🪙💰","🧠🧿🧘","🍩🍒🍓","📿🧘🛐","🌈🌟✨"
            ]

    /// Increment value range for random configuration
    static let incrementValueRange: ClosedRange<Int> = -100...100
    
    // MARK: - Animation Configuration
    /// Default animation duration
    static let defaultAnimationDuration: Double = 0.2

    /// Long animation duration
    static let longAnimationDuration: Double = 0.5

    // MARK: - Audio Trimming Configuration
    /// Minimum time gap between start and end time when trimming (in seconds)
    static let minimumTrimmingGap: Double = 0.1

    /// Default waveform circle size
    static let waveformCircleSize: CGFloat = 12

    /// Default corner radius for waveform controls
    static let waveformCornerRadius: CGFloat = 4

    // MARK: - Playback Rate Configuration
    /// Available playback rate presets
    static let playbackRatePresets: [Double] = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]

    // MARK: - Volume Configuration
    /// Available volume presets (as percentages)
    static let volumePresets: [Double] = [0.0, 0.25, 0.5, 0.75, 1.0]
    

    // MARK: - 音频配置
    /// 播放速度范围配置
   static let minPlaybackRate: Double = 0.25
   static let maxPlaybackRate: Double = 10.0
   static let playbackRateStep: Double = 0.2
    
    /// 音量范围配置
    static let minVolume: Double = 0.0
    static let maxVolume: Double = 5  // 允许音量增强
    static let volumeStep: Double = 0.2  // 播放强度步长，默认20%
    
    /// 波形配置
    static let waveformBars: Int = 80  // 波形条数量

    // MARK: - UI Element Sizes
    /// Small font size for preset buttons
    static let presetButtonFontSize: CGFloat = 10

    /// Preset button padding
    static let presetButtonHorizontalPadding: CGFloat = 6
    static let presetButtonVerticalPadding: CGFloat = 2
    
    // MARK: - Backtrack Configuration
    /// 回溯时长是否跟随播放速度缩放（true = 听感秒，false = 时间轴秒）
    static let backtrackFollowRate: Bool = false

    // MARK: - Feature Switches
    /// 异步持久化开关，如遇异常设为 false 立即回滚
    static let useAsyncSave: Bool = true
    
    // MARK: - Thumbnail Configuration
    /// 是否启用缩略图缓存，列表/网格场景使用
    static let useThumbnailCache: Bool = true
    /// 默认缩略图边长（点）
    static let defaultThumbnailSize: CGFloat = 40
    
    // MARK: - Sound Cache Configuration
    /// 是否启用音频 Data 缓存
    static let enableSoundDataCache: Bool = true
    
    /// 视图直接绑定 manager 开关（Phase4 灰度）
    static let useDirectManagerBinding: Bool = true
    
    /// SoundManager 是否异步加载配置
    static let useAsyncSoundLoad: Bool = true
    
    /// 是否使用 Swift Concurrency Task.sleep 替换 Timer（Phase4 energy 优化）
    static let useTaskTimer: Bool = true

    // MARK: - Performance Optimization Configuration
    /// 是否启用批量数据写入优化
    static let useBatchWrite: Bool = true
    /// 批量写入延迟时间（秒）
    static let batchWriteDelay: TimeInterval = 0.5
    /// 是否启用智能缩略图预加载
    static let useSmartThumbnailPreload: Bool = true
    /// 缩略图预加载范围（当前图片前后N张）
    static let thumbnailPreloadRange: Int = 2
    /// 是否启用视图预加载
    static let useViewPreload: Bool = true
    /// 视图预加载延迟时间（秒）
    static let viewPreloadDelay: TimeInterval = 0.5
    
    // MARK: - Persistence Keys
    struct UserDefaultsKeys {
        static let selectedColors = "selectedColors_"
        static let imageSettings = "imageSettings_"
        static let soundConfigs = "soundConfigs"
        static let customTriggerDisplays = "customTriggerDisplays"
    }
}
