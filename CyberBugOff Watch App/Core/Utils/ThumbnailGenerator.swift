import SwiftUI
import UIKit
import WatchKit
import CryptoKit

/// 生成与全屏视图效果一致的方形缩略图，并做内存缓存。
struct ThumbnailGenerator {
    private static let cache: NSCache<NSString, UIImage> = {
        let c = NSCache<NSString, UIImage>()
        let width = WKInterfaceDevice.current().screenBounds.width
        // 根据设备性能动态调整缓存容量
        c.countLimit = width > 200 ? 300 : 200 // 增加缓存容量
        // 设置内存限制（15MB，为Apple Watch优化）
        c.totalCostLimit = 15 * 1024 * 1024
        // 设置缓存清理策略
        c.evictsObjectsWithDiscardedContent = true
        return c
    }()

    // 预加载队列，避免阻塞主线程
    private static let preloadQueue = DispatchQueue(label: "com.cyberbugoff.thumbnail.preload", qos: .utility)

    /// 异步获取缩略图
    /// - Parameters:
    ///   - imageName: 图片名称（或自定义 key）
    ///   - size: 目标边长
    ///   - model: 数据模型，用于读取自定义 URL 与缩放信息
    static func thumbnail(for imageName: String, size: CGFloat, model: BugOffModel) async -> UIImage? {
        let scale = model.getImageScale(for: imageName)
        let offset = model.getImageOffset(for: imageName)
        // 缩略图应该与全屏视图保持一致，使用用户设置的缩放而不是effectiveScale

        // 获取主图圈选数据的哈希值（如果有）
        var circleSelectionHash = ""
        let config = model.getCustomTriggerDisplay(for: imageName)
        if let selectionData = config.mainCircleSelectionData, !selectionData.pathPoints.isEmpty {
            // 简单计算路径点的哈希值
            let pointsHash = selectionData.pathPoints.reduce(0) { result, point in
                result ^ point.x.hashValue ^ point.y.hashValue
            }
            circleSelectionHash = "-circle\(pointsHash)"
        }

        // 检查是否有图片占比设置（scale != 1.0 或 offset != .zero）
        let hasProportionSettings = scale != 1.0 || offset != .zero
        let proportionHash = hasProportionSettings ? "-prop\(Int(scale * 100))\(Int(offset.width))\(Int(offset.height))" : ""

        // 构造唯一 key
        let keyString = "\(imageName)-\(Int(size))-s\(String(format: "%.2f", scale))-o\(Int(offset.width))_\(Int(offset.height))\(circleSelectionHash)\(proportionHash)"
        // 使用 SHA256 防止文件名过长 / 包含非法字符
        let keyData = Data(keyString.utf8)
        let hash = SHA256.hash(data: keyData)
        let fileName = hash.compactMap { String(format: "%02x", $0) }.joined()
        let fileURL = cacheFolder.appendingPathComponent(fileName).appendingPathExtension("png")

        // 先读内存缓存
        if let cached = cache.object(forKey: fileName as NSString) { return cached }

        // 异步读取磁盘缓存，避免阻塞UI
        if FileManager.default.fileExists(atPath: fileURL.path) {
            // 在后台队列读取磁盘缓存
            let diskImg = await withCheckedContinuation { continuation in
                DispatchQueue.global(qos: .userInitiated).async {
                    let image = UIImage(contentsOfFile: fileURL.path)
                    continuation.resume(returning: image)
                }
            }

            if let diskImg = diskImg {
                // 将磁盘缓存加载到内存缓存
                cache.setObject(diskImg, forKey: fileName as NSString)
                return diskImg
            }
        }

        // 在闭包外部提取所有需要的数据，避免 Sendable 警告
        // 加载基础图片（内联 loadBaseImage 的逻辑）
        let base: UIImage
        if let selectionData = config.mainCircleSelectionData,
           !selectionData.pathPoints.isEmpty,
           let originalImage = model.imageManager.getOriginalImage(for: imageName) {

            // 应用圈选裁剪
            if let croppedImage = model.applyCircleSelectionToImage(
                originalImage,
                selectionData: selectionData,
                scale: config.mainImageScale,
                offset: config.mainImageOffset
            ) {
                base = croppedImage
            } else {
                // 如果圈选裁剪失败，使用标准的图片获取方法
                guard let fallbackImage = model.imageManager.getDisplayImage(for: imageName) else { return nil }
                base = fallbackImage
            }
        } else {
            // 如果没有圈选数据，使用标准的图片获取方法
            guard let standardImage = model.imageManager.getDisplayImage(for: imageName) else { return nil }
            base = standardImage
        }

        // 提取配置中的具体值
        let mainCircleSelectionData = config.mainCircleSelectionData
        let mainImageScale = config.mainImageScale
        let mainImageOffset = config.mainImageOffset

        // 获取屏幕尺寸用于后续计算
        let screenBounds = WKInterfaceDevice.current().screenBounds
        let screenAspectRatio = screenBounds.height / screenBounds.width

        // 使用更高效的 UIGraphics 渲染替代 ImageRenderer
        let rendered: UIImage? = await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let result = generateThumbnailWithUIGraphics(
                    base: base,
                    size: size,
                    userScale: scale,  // 使用用户设置的缩放，与全屏视图一致
                    offset: offset,
                    screenAspectRatio: screenAspectRatio,
                    imageName: imageName,
                    mainCircleSelectionData: mainCircleSelectionData,
                    mainImageScale: mainImageScale,
                    mainImageOffset: mainImageOffset,
                    hasProportionSettings: hasProportionSettings
                )
                continuation.resume(returning: result)
            }
        }

        guard let thumb = rendered else { return nil }

        // 不压缩图片，保持最高质量
        // 使用PNG格式确保无损存储
        let imageData = thumb.pngData()
        let cost = imageData?.count ?? 0
        cache.setObject(thumb, forKey: fileName as NSString, cost: cost)
        
        // 异步写入磁盘（保持原始质量）
        if let data = imageData {
            DispatchQueue.global(qos: .utility).async {
                try? data.write(to: fileURL, options: .atomic)
            }
        }
        return thumb
    }

    /// 批量预加载缩略图，提升列表滚动性能
    static func batchPreload(imageNames: [String], size: CGFloat, model: BugOffModel) {
        preloadQueue.async {
            for imageName in imageNames {
                Task {
                    _ = await thumbnail(for: imageName, size: size, model: model)
                }
            }
        }
    }

    /// 智能预加载：根据当前显示的图片预加载相邻图片
    static func smartPreload(currentImage: String, allImages: [String], size: CGFloat, model: BugOffModel) {
        guard let currentIndex = allImages.firstIndex(of: currentImage) else { return }

        preloadQueue.async {
            // 预加载前后各2张图片
            let preloadRange = max(0, currentIndex - 2)...min(allImages.count - 1, currentIndex + 2)

            for index in preloadRange {
                let imageName = allImages[index]
                Task {
                    _ = await thumbnail(for: imageName, size: size, model: model)
                }
            }
        }
    }

    /// 失效所有缩略图
    static func invalidateAll() {
        cache.removeAllObjects()
        clearDiskCache()
    }

    /// 失效特定图片的缩略图（当图片占比设置改变时使用）
    static func invalidateThumbnail(for imageName: String, model: BugOffModel) {
        // 由于NSCache没有提供遍历所有key的方法，我们采用简单的策略：
        // 清理整个内存缓存，让系统重新生成缩略图
        cache.removeAllObjects()

        // 清理磁盘缓存中匹配的项
        let fm = FileManager.default
        if let files = try? fm.contentsOfDirectory(atPath: cacheFolder.path) {
            for file in files {
                // 检查文件名是否包含图片名称的哈希
                // 由于我们使用SHA256哈希，无法直接从文件名判断原始图片名
                // 所以清理所有缓存文件，让系统重新生成
                let fileURL = cacheFolder.appendingPathComponent(file)
                try? fm.removeItem(at: fileURL)
            }
        }
    }

    /// 清理磁盘缓存
    static func clearDiskCache() {
        let fm = FileManager.default
        if fm.fileExists(atPath: cacheFolder.path) {
            try? fm.removeItem(at: cacheFolder)
        }
        // 重新创建文件夹
        try? fm.createDirectory(at: cacheFolder, withIntermediateDirectories: true)
    }


    
    /// 使用 UIGraphics 生成缩略图（比 ImageRenderer 更快）
    private static func generateThumbnailWithUIGraphics(
        base: UIImage,
        size: CGFloat,
        userScale: CGFloat,  // 用户设置的缩放，与全屏视图一致
        offset: CGSize,
        screenAspectRatio: CGFloat,
        imageName: String,
        mainCircleSelectionData: CircleSelectionData?,
        mainImageScale: CGFloat,
        mainImageOffset: CGSize,
        hasProportionSettings: Bool
    ) -> UIImage? {
        // 使用传入的屏幕比例生成矩形缩略图而不是方形
        let targetSize = CGSize(width: size, height: size * screenAspectRatio)
        // 使用更高的渲染分辨率来提升清晰度
        let renderScale = max(WKInterfaceDevice.current().screenScale, 2.0)

        UIGraphicsBeginImageContextWithOptions(targetSize, false, renderScale)
        defer { UIGraphicsEndImageContext() }

        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        // 设置黑色背景，与全屏视图保持一致
        context.setFillColor(UIColor.black.cgColor)
        context.fill(CGRect(origin: .zero, size: targetSize))
        
        // 计算绘制参数
        let imageSize = base.size
        
        // 根据图片类型和设置选择合适的缩放模式
        let isCircleSelectionImage = mainCircleSelectionData != nil && !mainCircleSelectionData!.pathPoints.isEmpty

        let scaledSize: CGSize

        if isCircleSelectionImage {
            // 圈选图片：模拟全屏视图的scaledToFit + scaleEffect逻辑
            let fitSize = calculateAspectFitSize(imageSize, targetSize: targetSize)
            scaledSize = CGSize(
                width: fitSize.width * userScale,
                height: fitSize.height * userScale
            )
        } else if hasProportionSettings {
            // 普通图片有占比设置：模拟全屏视图的scaledToFill + scaleEffect逻辑
            let fillSize = calculateAspectFillSize(imageSize, targetSize: targetSize)
            scaledSize = CGSize(
                width: fillSize.width * userScale,
                height: fillSize.height * userScale
            )
        } else {
            // 普通图片无占比设置：使用AspectFill模式填充容器
            scaledSize = calculateAspectFillSize(imageSize, targetSize: targetSize)
        }
        
        // 修正偏移量计算，考虑矩形缩略图与实际屏幕的比例关系
        let screenW = WKInterfaceDevice.current().screenBounds.width
        let screenH = WKInterfaceDevice.current().screenBounds.height
        let ratioX = targetSize.width / screenW
        let ratioY = targetSize.height / screenH

        // 应用偏移，使用正确的比例映射
        let offsetX = (targetSize.width - scaledSize.width) / 2 + offset.width * ratioX
        let offsetY = (targetSize.height - scaledSize.height) / 2 + offset.height * ratioY
        
        let drawRect = CGRect(
            x: offsetX,
            y: offsetY,
            width: scaledSize.width,
            height: scaledSize.height
        )

        // 设置裁剪区域为缩略图边界，确保显示完整内容
        context.addRect(CGRect(origin: .zero, size: targetSize))
        context.clip()

        // 绘制图片
        base.draw(in: drawRect)
        
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    /// 计算 AspectFill 尺寸
    private static func calculateAspectFillSize(_ imageSize: CGSize, targetSize: CGSize) -> CGSize {
        let widthRatio = targetSize.width / imageSize.width
        let heightRatio = targetSize.height / imageSize.height
        let scale = max(widthRatio, heightRatio)
        
        return CGSize(
            width: imageSize.width * scale,
            height: imageSize.height * scale
        )
    }
    
    /// 计算 AspectFit 尺寸
    private static func calculateAspectFitSize(_ imageSize: CGSize, targetSize: CGSize) -> CGSize {
        let widthRatio = targetSize.width / imageSize.width
        let heightRatio = targetSize.height / imageSize.height
        let scale = min(widthRatio, heightRatio)
        
        return CGSize(
            width: imageSize.width * scale,
            height: imageSize.height * scale
        )
    }

    // 磁盘缓存目录
    private static let cacheFolder: URL = {
        let fm = FileManager.default
        let dir = fm.urls(for: .cachesDirectory, in: .userDomainMask).first!.appendingPathComponent("thumbnails")
        if !fm.fileExists(atPath: dir.path) {
            try? fm.createDirectory(at: dir, withIntermediateDirectories: true)
        }
        return dir
    }()

    /// 预加载缩略图到内存缓存（减少启动时的闪烁）
    static func preloadThumbnails(for imageNames: [String], size: CGFloat, model: BugOffModel) {
        Task {
            for imageName in imageNames.prefix(5) { // 只预加载前5个，避免内存压力
                _ = await thumbnail(for: imageName, size: size, model: model)
            }
        }
    }

    /// 清理缓存
    static func clearCache() {
        cache.removeAllObjects()

        // 清理磁盘缓存
        try? FileManager.default.removeItem(at: cacheFolder)
    }
}
