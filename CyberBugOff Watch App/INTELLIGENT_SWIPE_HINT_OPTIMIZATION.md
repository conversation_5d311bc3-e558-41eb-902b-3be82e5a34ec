# 智能侧滑提示动画优化

## 优化目标

让侧滑提示动画根据实际的侧滑按钮配置智能展示相应方向的动画，而不是使用固定的双向动画。

## 优化前的问题

- **固定动画样式**：所有场景都使用 `.bidirectional(.right)` 双向动画
- **不匹配实际操作**：即使某个方向没有侧滑按钮，也会显示该方向的动画
- **用户困惑**：动画提示与实际可用操作不符

## 智能动画逻辑

### 动画样式决策树

```
检查侧滑按钮配置
├── 左滑 + 右滑都有 → 双向动画 (.bidirectional(.right))
├── 只有左滑按钮 → 单向右滑动画 (.single(.right))
├── 只有右滑按钮 → 单向左滑动画 (.single(.left))
└── 没有侧滑按钮 → 摆动动画 (.wiggle)
```

### 侧滑按钮检测逻辑

#### 左滑按钮检测 (`hasLeadingSwipeActions`)
```swift
private var hasLeadingSwipeActions: Bool {
    // 显示编辑按钮：合成视图(nil)、首页音效列表(.edit)
    return mode == nil || mode == .edit
}
```

#### 右滑按钮检测 (`hasTrailingSwipeActions`)
```swift
private var hasTrailingSwipeActions: Bool {
    // mode设置模式：右滑显示设置按钮
    if mode == .modeSettings {
        return true
    }
    
    // 检查删除按钮（音效合成模式且只剩一个音效时不显示）
    let hasDeleteButton = !(mixerSelectedSounds?.count ?? 0 <= 1 && mode == nil)
    
    // 检查复制按钮（只在合成视图且非同时播放模式下显示）
    let hasDuplicateButton = mode == nil && duplicateSound != nil
    
    return hasDeleteButton || hasDuplicateButton
}
```

## 不同场景的动画效果

### 1. 音效列表视图 - 编辑模式 (.edit)
- **左滑**：编辑按钮 ✅
- **右滑**：删除按钮 ✅
- **动画**：双向动画 (.bidirectional(.right))

### 2. 音效列表视图 - 设置模式 (.modeSettings)
- **左滑**：无按钮 ❌
- **右滑**：设置按钮 ✅
- **动画**：单向左滑动画 (.single(.left))

### 3. 音效合成视图 - 多个音效
- **左滑**：编辑按钮 ✅
- **右滑**：删除按钮 + 复制按钮 ✅
- **动画**：双向动画 (.bidirectional(.right))

### 4. 音效合成视图 - 只剩一个音效
- **左滑**：编辑按钮 ✅
- **右滑**：复制按钮 ✅（删除按钮隐藏）
- **动画**：双向动画 (.bidirectional(.right))

### 5. 特殊情况 - 无侧滑按钮
- **左滑**：无按钮 ❌
- **右滑**：无按钮 ❌
- **动画**：摆动动画 (.wiggle)

## 技术实现

### 智能样式计算
```swift
private var intelligentHintStyle: AppTheme.SwipeHintStyle {
    let hasLeadingActions = hasLeadingSwipeActions
    let hasTrailingActions = hasTrailingSwipeActions
    
    if hasLeadingActions && hasTrailingActions {
        // 两边都有按钮：使用双向动画
        return .bidirectional(.right)
    } else if hasLeadingActions {
        // 只有左滑按钮：使用单向右滑动画（提示左滑操作）
        return .single(.right)
    } else if hasTrailingActions {
        // 只有右滑按钮：使用单向左滑动画（提示右滑操作）
        return .single(.left)
    } else {
        // 没有侧滑按钮：使用摆动动画
        return .wiggle
    }
}
```

### 动画触发优化
```swift
manager.performHint(
    for: sound,
    offset: offsetBinding,
    style: intelligentHintStyle // 使用智能计算的样式
)
```

## 用户体验提升

### 1. 精准的操作指导
- **准确提示**：动画方向与实际可用操作完全匹配
- **减少困惑**：用户不会被误导去尝试不存在的操作
- **提高效率**：用户能快速理解可用的交互方式

### 2. 情境感知
- **动态适应**：根据当前模式和状态调整动画
- **智能判断**：考虑音效数量、播放模式等因素
- **一致体验**：不同场景下都有合适的提示

### 3. 更自然的交互
- **符合预期**：动画行为与UI逻辑保持一致
- **减少学习成本**：用户无需记忆复杂的交互规则
- **直观明了**：一看就知道可以进行什么操作

## 兼容性保证

- ✅ **向后兼容**：保留原有的 `hintStyle` 参数接口
- ✅ **渐进增强**：现有代码无需修改即可获得智能动画
- ✅ **灵活配置**：如需要仍可手动指定动画样式
- ✅ **性能优化**：智能计算逻辑轻量高效

## 测试场景

### 基本功能测试
1. **音效列表 - 编辑模式**：验证双向动画
2. **音效列表 - 设置模式**：验证单向左滑动画
3. **音效合成 - 多音效**：验证双向动画
4. **音效合成 - 单音效**：验证双向动画（无删除按钮）

### 边界情况测试
1. **动态变化**：音效数量变化时动画样式的更新
2. **模式切换**：不同模式间切换时的动画适应
3. **特殊状态**：无侧滑按钮时的摆动动画

### 交互测试
1. **动画准确性**：动画方向与实际按钮位置匹配
2. **用户理解**：用户能否通过动画快速理解可用操作
3. **操作流畅性**：动画是否有助于提升操作效率

## 后续优化建议

1. **动画参数调优**：根据用户反馈调整动画幅度和速度
2. **更多样式支持**：考虑添加更多动画样式选项
3. **个性化设置**：允许用户自定义动画偏好
4. **数据分析**：收集用户交互数据优化动画策略
