# Mode数据结构详细说明

## 概述

在CyberBugOff应用中，一个Mode代表一个独立的交互模式，每个Mode都有完整的配置隔离和独立的状态管理。Mode支持单图片模式和连环画模式，具有丰富的配置选项和扩展能力。

## 核心数据结构

### 1. ModeContext（Mode上下文）

```swift
struct ModeContext: Codable, Hashable {
    let modeId: String        // Mode的唯一标识符
    let modeType: String      // Mode类型："image", "sound", "combo"等
    
    // 默认mode上下文（向后兼容）
    static let default = ModeContext(modeId: "default", modeType: "image")
    
    // 生成配置存储键值
    func configKey(for imageName: String) -> String {
        return "mode_\(modeId)_image_\(imageName)"
    }
}
```

**作用**：
- 提供Mode级别的配置隔离
- 支持多种Mode类型的扩展
- 确保配置存储的唯一性和可追溯性

### 2. ImageSettings（图片设置）

```swift
struct ImageSettings: Codable {
    // MARK: - 基础配置
    var baseImageName: String = ""              // 关联的原图名称
    var displayName: String = ""                // 用户自定义显示名称
    var modeContext: ModeContext?               // 所属Mode上下文
    var configVersion: Int = 2                  // 配置版本号
    
    // MARK: - 触发配置
    var triggerMode: ImageTriggerMode = .tap    // 触发模式：点击/摇晃/自动
    var showClickCount: Bool = false            // 是否显示点击计数
    var clickCount: Int = 0                     // 当前点击次数
    var customTriggerDisplay: CustomTriggerDisplay = CustomTriggerDisplay()
    
    // MARK: - 视觉配置
    var scale: CGFloat = 1.0                    // 图片缩放比例
    var offset: CGSize = .zero                  // 图片偏移量
    
    // MARK: - 音效配置
    var soundConfigs: [String: SoundConfig] = [:] // 每个图片独立的音效配置
    
    // MARK: - 多图片支持（连环画模式）
    var modeType: ImageModeType = .single       // 模式类型：单图片/连环画
    var imageSequence: [String] = []            // 图片序列
    var currentImageIndex: Int = 0              // 当前图片索引
    var navigationMode: SequenceNavigationMode = .manual  // 导航模式
    var autoSwitchInterval: Double = 3.0        // 自动切换间隔
}
```

### 3. 支持的枚举类型

#### ImageTriggerMode（触发模式）
```swift
public enum ImageTriggerMode: String, CaseIterable, Identifiable, Codable {
    case tap = "点击触发"      // 手动点击触发
    case shake = "摇晃触发"    // 设备摇晃触发
    case auto = "自动播放"     // 自动循环播放
}
```

#### ImageModeType（图片模式类型）
```swift
public enum ImageModeType: String, CaseIterable, Identifiable, Codable {
    case single = "单图片模式"    // 单张图片模式
    case sequence = "连环画模式"  // 多图片序列模式
}
```

#### SequenceNavigationMode（序列导航模式）
```swift
public enum SequenceNavigationMode: String, CaseIterable, Identifiable, Codable {
    case manual = "手动切换"      // 手动切换图片
    case autoNext = "自动下一张"  // 自动切换到下一张
    case loop = "循环播放"        // 循环播放所有图片
}
```

### 4. CustomTriggerDisplay（自定义触发显示）

```swift
struct CustomTriggerDisplay: Codable, Equatable {
    var isEnabled: Bool = false                 // 是否启用自定义显示
    var customText: String = ""                 // 自定义文本
    var incrementValue: Int = 1                 // 增量值
    var displayColor: String = "white"          // 显示颜色
    var emoji: String = "🍀"                   // 表情符号
    var animationStyle: TriggerAnimationStyle = .bounce  // 动画样式
    var showIncrement: Bool = true              // 是否显示增量数字
    var fontSize: Double = 16.0                 // 字体大小
}
```

### 5. SoundConfig（音效配置）

```swift
struct SoundConfig: Codable, Equatable, Identifiable {
    var name: String                    // 显示名称（可编辑）
    let baseSoundName: String          // 原始音效文件名（不可编辑）
    var playbackRate: Double = 1.0     // 播放速度
    var volume: Double = 0.8           // 音量
    var startTime: TimeInterval = 0.0  // 开始时间
    var endTime: TimeInterval?         // 结束时间（nil表示播放到结尾）
}
```

## Mode的生命周期

### 1. Mode创建
```swift
// 创建新Mode的上下文
let newModeContext = ModeContext(modeId: "bug1_copy_123456")

// 初始化Mode设置
var newSettings = ImageSettings(modeContext: newModeContext)
newSettings.baseImageName = "bug1"
newSettings.displayName = "Bug1 副本"
```

### 2. Mode配置管理
```swift
// 获取Mode配置
let settings = imageManager.getImageSettings(for: modeName, in: modeContext)

// 更新Mode配置
imageManager.updateImageSettings(for: modeName, in: modeContext, settings: settings)
```

### 3. Mode复制（配置隔离）
```swift
func cloneModeWithIsolation(_ imageName: String) -> String? {
    // 1. 生成新的唯一mode名称
    let newModeName = "\(imageName)_copy_\(timestamp)"
    
    // 2. 创建新的mode上下文
    let newModeContext = ModeContext(modeId: newModeName)
    
    // 3. 复制原mode的所有配置
    let originalSettings = imageManager.getImageSettings(for: imageName, in: originalModeContext)
    var newSettings = originalSettings
    
    // 4. 设置新配置的关联关系和隔离
    newSettings.baseImageName = originalSettings.baseImageName.isEmpty ? imageName : originalSettings.baseImageName
    newSettings.modeContext = newModeContext
    newSettings.clickCount = 0  // 重置状态
    
    // 5. 保存新mode配置
    imageManager.updateImageSettings(for: newModeName, in: newModeContext, settings: newSettings)
}
```

## 数据存储结构

### 1. 配置存储键值格式
```
新格式：mode_{modeId}_image_{imageName}
示例：mode_bug1_copy_123456_image_bug1_copy_123456

旧格式（向后兼容）：imageSettings{imageName}
示例：imageSettingsbug1
```

### 2. 音效关联存储
```swift
// BugOffModel中的音效关联
var imageMultiSounds: [String: [String]] = [:]
// 键：Mode名称，值：关联的音效名称列表

// 示例
imageMultiSounds["bug1_copy_123456"] = ["sound1", "sound2", "合成音效1"]
```

### 3. 触发器配置存储
```swift
// TriggerManager中的自定义触发显示
var customTriggerDisplays: [String: CustomTriggerDisplay] = [:]
// 键：Mode名称，值：自定义触发显示配置
```

## Mode的特性和能力

### 1. 配置隔离
- **完全独立**：每个Mode有独立的配置空间
- **状态隔离**：点击次数、播放状态等完全隔离
- **音效隔离**：每个Mode可以有独立的音效配置

### 2. 图片关联
- **baseImageName**：指向实际的图片文件
- **显示复用**：多个Mode可以使用同一张图片
- **配置独立**：使用相同图片的Mode配置完全独立

### 3. 多图片支持（连环画模式）
- **图片序列**：支持多张图片的有序播放
- **导航模式**：手动切换、自动播放、循环播放
- **状态管理**：当前图片索引、切换间隔等

### 4. 扩展性设计
- **Mode类型**：支持image、sound、combo等多种类型
- **版本管理**：configVersion支持配置迁移
- **向后兼容**：完全兼容旧版本数据格式

## 实际应用场景

### 1. 单图片Mode
```
Mode: "bug1"
├── baseImageName: "bug1"
├── displayName: "小虫子"
├── triggerMode: .tap
├── soundConfigs: ["sound1": SoundConfig, "sound2": SoundConfig]
└── modeType: .single
```

### 2. 复制Mode（配置隔离）
```
Mode: "bug1_copy_123456"
├── baseImageName: "bug1"          // 指向原图
├── displayName: "小虫子 副本"
├── triggerMode: .shake             // 独立配置
├── soundConfigs: [...]             // 独立音效配置
├── clickCount: 0                   // 独立状态
└── modeContext: ModeContext(modeId: "bug1_copy_123456")
```

### 3. 连环画Mode
```
Mode: "story1"
├── baseImageName: ""
├── displayName: "故事模式"
├── modeType: .sequence
├── imageSequence: ["page1", "page2", "page3"]
├── currentImageIndex: 0
├── navigationMode: .autoNext
└── autoSwitchInterval: 3.0
```

这种数据结构设计确保了Mode系统的灵活性、可扩展性和配置隔离能力，为未来的功能扩展（如连环画模式、组合模式等）提供了坚实的基础。
